package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.UserBo;
import org.dromara.pdkj.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/pdkj/user")
@Validated
@RequiredArgsConstructor
@SaIgnore
public class UserController extends BaseController {

    @Autowired
    private IUserService userService;

    @PostMapping("/sync")
    public void syncUserInfo(@RequestBody UserBo userBo) {
        userService.sync(userBo);
    }
}
