package org.dromara.common.mybatis.handler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.dromara.common.core.utils.StringUtils;

import java.lang.reflect.Field;

@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class HutoolJsonArrayTypeHandler extends AbstractJsonTypeHandler<Object> {

    public HutoolJsonArrayTypeHandler(Class<?> type) {
        super(type);
    }

    public HutoolJsonArrayTypeHandler(Class<?> type, Field field) {
        super(type, field);
    }

    @Override
    public Object parse(String json) {
        if (StringUtils.isBlank(json) || "null".equals(json)) {
            return new JSONArray();
        }
        return JSONUtil.parseArray(json);
    }

    @Override
    public String toJson(Object obj) {
        return JSONUtil.toJsonStr(obj);
    }
}
