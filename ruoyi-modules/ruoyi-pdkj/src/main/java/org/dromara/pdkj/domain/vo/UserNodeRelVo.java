package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.UserNodeRel;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 住户管理视图对象 tb_node_user_rel
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserNodeRel.class)
public class UserNodeRelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 楼栋ID
     */
    @ExcelProperty(value = "楼栋ID")
    private Long nodeId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * APP授权状态
     */
    @ExcelProperty(value = "APP授权状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pdkj_resident_auth_type")
    private Long appAuthStatus;

    /**
     * APP授权开始时间
     */
    @ExcelProperty(value = "APP授权开始时间")
    private Date appAuthStart;

    /**
     * APP授权结束时间
     */
    @ExcelProperty(value = "APP授权结束时间")
    private Date appAuthEnd;

    /**
     * 人脸授权状态
     */
    @ExcelProperty(value = "人脸授权状态")
    private Long faceAuthStatus;

    /**
     * 人脸授权开始
     */
    @ExcelProperty(value = "人脸授权开始")
    private Date faceAuthStart;

    /**
     * 人脸授权结束
     */
    @ExcelProperty(value = "人脸授权结束")
    private Date faceAuthEnd;

    /**
     * 楼栋路径
     */
    @ExcelProperty(value = "楼栋路径")
    private String nodePath;

    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区ID")
    private Long estateId;


}
