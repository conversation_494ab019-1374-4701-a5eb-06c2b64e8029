package org.dromara.mqtt.adapter.tcp;

import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.collect.base.IDeviceProtocol;
import org.dromara.collect.service.ProtocolService;
import org.dromara.common.core.enums.ServerType;
import org.dromara.common.core.mq.DeviceReport;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.common.core.mq.message.DeviceData;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.mq.redischannel.producer.MessageProducer;
import org.dromara.mqtt.manager.SessionManger;
import org.dromara.mqtt.model.lancen.LancenDeviceBasicInfo;
import org.dromara.mqtt.model.lancen.LancenDeviceProperties;
import org.dromara.pdkj.domain.DeviceInventory;
import org.dromara.pdkj.domain.vo.ProductVo;
import org.dromara.pdkj.mapper.DeviceInventoryMapper;
import org.dromara.pdkj.mapper.DeviceMapper;
import org.dromara.pdkj.mapper.ProductMapper;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2020/4/14 15:31:12
 */
@Component
@Slf4j
@ChannelHandler.Sharable
public class TcpMessageAdapter extends SimpleChannelInboundHandler<byte[]> {
    private static TcpMessageAdapter adapter;

    @Resource
    protected ProductMapper productMapper;
    @Resource
    protected IDeviceService deviceService;
    @Resource
    protected DeviceMapper deviceMapper;
    @Resource
    protected ProtocolService protocolService;
    @Resource
    protected DeviceInventoryMapper deviceInventoryMapper;


    @PostConstruct
    public void init() {
        adapter = this;
        adapter.productMapper = productMapper;
        adapter.deviceService = deviceService;
        adapter.deviceMapper = deviceMapper;
        adapter.protocolService = protocolService;
        adapter.deviceInventoryMapper = deviceInventoryMapper;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, byte[] msg) {
        log.info("收到客户端【{}】的原始数据 --> {} ", ctx.channel().id().asLongText(), HexUtil.encodeHexStr(msg));

        // 上报配置后或者获取配置去注册设备
        if ((msg[0] == 0x10 && msg[1] == 0x27) || (msg[0] == 0x30 && msg[1] == 0x00)) {
            handleDeviceRegister(ctx, msg);
        }

        // 心跳
        if (msg[0] == 0x2e && msg[1] == 0x00) {
            handleHeartbeat(ctx, msg);
            return;
        }

        // 未注册的连接给它断开
        String clientId = AttributeUtils.getClientId(ctx.channel());
        if (StringUtils.isEmpty(clientId)) {
            log.error("未注册的连接给它断开[{}]", clientId);
            ctx.channel().close();
            return;
        }

        DeviceInventory device = adapter.deviceInventoryMapper.selectOne(new LambdaQueryWrapper<DeviceInventory>().eq(DeviceInventory::getDeviceCode, clientId));
        if (device == null) {
            log.error("未入库的连接给它断开[{}]", clientId);
            ctx.channel().close();
            return;
        }

        ProductVo productVo = adapter.productMapper.selectVoById(device.getProductId());
        if (productVo == null) {
            log.error("没有产品的连接给它断开[{}]", clientId);
            ctx.channel().close();
            return;
        }

        IDeviceProtocol protocol = adapter.protocolService.getProtocol(productVo.getProtocolCode());
        if (protocol == null) {
            log.error("没有协议的给它断开[{}]", productVo.getProtocolCode());
            ctx.channel().close();
            return;
        }

        byte[] checked = protocol.checkDevice(clientId);
        if (checked != null) {
            log.error("设备[{}]校验失败", clientId);
            ctx.channel().writeAndFlush(Unpooled.copiedBuffer(checked));
            return;
        }

        DeviceReport decode = protocol.decode(DeviceData.builder().productId(productVo.getId()).deviceCode(clientId).data(msg).build(), clientId);
        if (decode == null) {
            log.info("无需处理[{}]", clientId);
            return;
        }

        if (decode.getResponseData() != null) {
            log.info("回复数据：{}, {}, {}", clientId, HexUtil.encodeHexStr(decode.getResponseData()), ctx.channel().id().asLongText());
            ctx.channel().writeAndFlush(Unpooled.copiedBuffer(decode.getResponseData()));
        }

        DeviceReportBo reportBo = DeviceReportBo.builder()
            .command(decode.getCommand())
            .body(decode.getBody())
            .data(JSONUtil.toJsonStr(decode.getValuesInput().getThingsModelSimpleItem()).getBytes(StandardCharsets.UTF_8))
            .deviceCode(clientId)
            .platformDate(DateUtils.getNowDate())
            .serverType(ServerType.TCP)
            .productId(productVo.getId())
            .productSymbol(productVo.getProductSymbol())
            .topicName(decode.getTopic())
            .build();
        MessageProducer.sendOtherMsg(reportBo);

    }

    private void handleDeviceRegister(ChannelHandlerContext ctx, byte[] msg) {
        byte[] bytes = new byte[msg.length - 8];
        System.arraycopy(msg, 8, bytes, 0, bytes.length);
        LancenDeviceBasicInfo properties = JSONUtil.toBean(new String(bytes), LancenDeviceBasicInfo.class);
        buildSession(ctx, properties.getUid(), false);
    }

    private void handleHeartbeat(ChannelHandlerContext ctx, byte[] msg) {
        ctx.channel().writeAndFlush(Unpooled.copiedBuffer(HexUtil.decodeHex("2f00000000000000")));
    }

    private void handleUploadProperties(ChannelHandlerContext ctx, byte[] msg) {
        byte[] bytes = new byte[msg.length - 8];
        System.arraycopy(msg, 8, bytes, 0, bytes.length);
        LancenDeviceProperties properties = JSONUtil.toBean(new String(bytes), LancenDeviceProperties.class);
        buildSession(ctx, properties.getUID(), false);
    }

    private void buildSession(ChannelHandlerContext ctx, String clientId, boolean insertEvent) {
        /*获取session*/
        Session session = new Session();
        session.setHandlerContext(ctx);
        session.setClientId(clientId);
        session.setConnected_at(DateUtils.getNowDate());
        InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        session.setIp(socketAddress.getAddress().getHostAddress());
        session.setServerType(ServerType.TCP);
//        SessionManger.removeClient(clientId, false);
        /*保存ClientId 和 session 到Attribute*/
        AttributeUtils.setClientId(ctx.channel(), clientId);
        AttributeUtils.setSession(ctx.channel(), session);
        session.setConnected(true);

        SessionManger.buildSession(clientId, session, insertEvent);
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) {

    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info("channelUnregistered[{}]下线了", channel.id().asLongText());
        SessionManger.removeContextByContext(ctx);
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info("channelActive[{}]上线了", channel.id().asLongText());
        //channelGroup.writeAndFlush("[服务器]:" + channel.remoteAddress() + "上线了\r\n");
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info("channelInactive[{}]离开了", channel.id().asLongText());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }

}
