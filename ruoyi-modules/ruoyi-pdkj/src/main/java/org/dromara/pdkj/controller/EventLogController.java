package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.EventLogBo;
import org.dromara.pdkj.domain.vo.EventLogVo;
import org.dromara.pdkj.service.IEventLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 事件日志
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/eventLog")
public class EventLogController extends BaseController {

    private final IEventLogService eventLogService;

    /**
     * 查询事件日志列表
     */
    @SaCheckPermission("pdkj:eventLog:list")
    @GetMapping("/list")
    public TableDataInfo<EventLogVo> list(EventLogBo bo, PageQuery pageQuery) {
        return eventLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出事件日志列表
     */
    @SaCheckPermission("pdkj:eventLog:export")
    @Log(title = "事件日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EventLogBo bo, HttpServletResponse response) {
        List<EventLogVo> list = eventLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "事件日志", EventLogVo.class, response);
    }

    /**
     * 获取事件日志详细信息
     *
     * @param logId 主键
     */
    @SaCheckPermission("pdkj:eventLog:query")
    @GetMapping("/{logId}")
    public R<EventLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long logId) {
        return R.ok(eventLogService.queryById(logId));
    }

    /**
     * 新增事件日志
     */
    @SaCheckPermission("pdkj:eventLog:add")
    @Log(title = "事件日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EventLogBo bo) {
        return toAjax(eventLogService.insertByBo(bo));
    }

    /**
     * 修改事件日志
     */
    @SaCheckPermission("pdkj:eventLog:edit")
    @Log(title = "事件日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EventLogBo bo) {
        return toAjax(eventLogService.updateByBo(bo));
    }

    /**
     * 删除事件日志
     *
     * @param logIds 主键串
     */
    @SaCheckPermission("pdkj:eventLog:remove")
    @Log(title = "事件日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] logIds) {
        return toAjax(eventLogService.deleteWithValidByIds(List.of(logIds), true));
    }
}
