package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.UserNodeRel;

import java.util.Date;

/**
 * 住户管理业务对象 tb_node_user_rel
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserNodeRel.class, reverseConvertGenerate = false)
public class UserNodeRelBo extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 楼栋ID
     */
    @NotNull(message = "楼栋ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long nodeId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * APP授权状态
     */
    @NotNull(message = "APP授权状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long appAuthStatus;

    /**
     * APP授权开始时间
     */
    @NotNull(message = "APP授权开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date appAuthStart;

    /**
     * APP授权结束时间
     */
    @NotNull(message = "APP授权结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date appAuthEnd;

    /**
     * 人脸授权状态
     */
    @NotNull(message = "人脸授权状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long faceAuthStatus;

    /**
     * 人脸授权开始
     */
    @NotNull(message = "人脸授权开始不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date faceAuthStart;

    /**
     * 人脸授权结束
     */
    @NotNull(message = "人脸授权结束不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date faceAuthEnd;

    /**
     * 楼栋路径
     */
    private String nodePath;

    /**
     * 小区ID
     */
    private Long estateId;


}
