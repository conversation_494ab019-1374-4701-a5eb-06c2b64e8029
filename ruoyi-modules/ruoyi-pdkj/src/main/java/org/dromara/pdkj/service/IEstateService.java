package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.EstateBo;
import org.dromara.pdkj.domain.vo.EstateVo;

import java.util.Collection;
import java.util.List;

/**
 * 小区信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IEstateService {

    /**
     * 查询小区信息
     *
     * @param id 主键
     * @return 小区信息
     */
    EstateVo queryById(Integer id);

    /**
     * 分页查询小区信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 小区信息分页列表
     */
    TableDataInfo<EstateVo> queryPageList(EstateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的小区信息列表
     *
     * @param bo 查询条件
     * @return 小区信息列表
     */
    List<EstateVo> queryList(EstateBo bo);

    /**
     * 新增小区信息
     *
     * @param bo 小区信息
     * @return 是否新增成功
     */
    Boolean insertByBo(EstateBo bo);

    /**
     * 修改小区信息
     *
     * @param bo 小区信息
     * @return 是否修改成功
     */
    Boolean updateByBo(EstateBo bo);

    /**
     * 校验并批量删除小区信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);
}
