package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.Protocol;

import java.io.Serial;
import java.io.Serializable;



/**
 * 协议管理视图对象 tb_protocol
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Protocol.class)
public class ProtocolVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ExcelProperty(value = "自增id")
    private Long id;

    /**
     * 协议编码
     */
    @ExcelProperty(value = "协议编码")
    private String protocolCode;

    /**
     * 协议名称
     */
    @ExcelProperty(value = "协议名称")
    private String protocolName;

    /**
     * 协议jar包,js包，c程序上传地址
     */
    @ExcelProperty(value = "协议jar包,js包，c程序上传地址")
    private String protocolFileUrl;

    /**
     * 协议类型 0:未知 1:jar，2.js,3.c
     */
    @ExcelProperty(value = "协议类型 0:未知 1:jar，2.js,3.c", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_protocol_type")
    private Integer protocolType;

    /**
     * 协议文件摘要(文件的md5)
     */
    @ExcelProperty(value = "协议文件摘要(文件的md5)")
    private String jarSign;

    /**
     * 0:草稿 1:启用 2:停用
     */
    @ExcelProperty(value = "0:草稿 1:启用 2:停用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_protocol_status")
    private Long protocolStatus;


}
