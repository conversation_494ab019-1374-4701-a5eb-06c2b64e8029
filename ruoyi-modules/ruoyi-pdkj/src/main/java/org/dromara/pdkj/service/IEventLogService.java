package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.EventLogBo;
import org.dromara.pdkj.domain.vo.EventLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 事件日志Service接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface IEventLogService {

    /**
     * 查询事件日志
     *
     * @param logId 主键
     * @return 事件日志
     */
    EventLogVo queryById(Long logId);

    /**
     * 分页查询事件日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 事件日志分页列表
     */
    TableDataInfo<EventLogVo> queryPageList(EventLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的事件日志列表
     *
     * @param bo 查询条件
     * @return 事件日志列表
     */
    List<EventLogVo> queryList(EventLogBo bo);

    /**
     * 新增事件日志
     *
     * @param bo 事件日志
     * @return 是否新增成功
     */
    Boolean insertByBo(EventLogBo bo);

    /**
     * 修改事件日志
     *
     * @param bo 事件日志
     * @return 是否修改成功
     */
    Boolean updateByBo(EventLogBo bo);

    /**
     * 校验并批量删除事件日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
