package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.ProductAuthorizeAddBo;
import org.dromara.pdkj.domain.bo.ProductAuthorizeBo;
import org.dromara.pdkj.domain.vo.ProductAuthorizeVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品授权码Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IProductAuthorizeService {

    /**
     * 查询产品授权码
     *
     * @param id 主键
     * @return 产品授权码
     */
    ProductAuthorizeVo queryById(Long id);

    /**
     * 分页查询产品授权码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品授权码分页列表
     */
    TableDataInfo<ProductAuthorizeVo> queryPageList(ProductAuthorizeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品授权码列表
     *
     * @param bo 查询条件
     * @return 产品授权码列表
     */
    List<ProductAuthorizeVo> queryList(ProductAuthorizeBo bo);

    /**
     * 新增产品授权码
     *
     * @param bo 产品授权码
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductAuthorizeBo bo);

    /**
     * 修改产品授权码
     *
     * @param bo 产品授权码
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductAuthorizeBo bo);

    /**
     * 校验并批量删除产品授权码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean createProductAuthorizeByNum(ProductAuthorizeAddBo bo);
}
