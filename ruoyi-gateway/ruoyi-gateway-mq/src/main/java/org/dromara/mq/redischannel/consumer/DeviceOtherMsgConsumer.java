package org.dromara.mq.redischannel.consumer;


import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.mq.service.impl.DeviceOtherMsgHandler;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/2/27 14:33
 */
@Slf4j
@Component
public class DeviceOtherMsgConsumer {

    @Resource
    private DeviceOtherMsgHandler otherMsgHandler;

    @Async(PdkjConstant.TASK.DEVICE_OTHER_TASK)
    public void consume(DeviceReportBo bo){
        try {
             //处理emq订阅的非 property/post 属性上报的消息 ，因为其他消息量小，放在一起处理
            otherMsgHandler.messageHandler(bo);
        }catch (Exception e){
            log.error("=>设备其他消息处理出错",e);
        }
    }

}
