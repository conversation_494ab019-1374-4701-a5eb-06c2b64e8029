package org.dromara.pdkj.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.DataEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.thingsModel.*;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.TypeUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisKeyBuilder;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.pdkj.domain.*;
import org.dromara.pdkj.domain.bo.DeviceBo;
import org.dromara.pdkj.domain.dto.PropertyDto;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.mapper.*;
import org.dromara.pdkj.service.IDeviceService;
import org.dromara.pdkj.service.IThingsModelService;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@RequiredArgsConstructor
@Service
public class DeviceServiceImpl implements IDeviceService {

    private final DeviceMapper baseMapper;
    private final ProductMapper productMapper;
    private final EstateMapper estateMapper;
    private final NodeMapper nodeMapper;
    private final IThingsModelService thingsModelService;
    private final EventLogMapper eventLogMapper;
    private final DeviceNodeRelMapper deviceNodeRelMapper;

    /**
     * 查询设备管理
     *
     * @param id 主键
     * @return 设备管理
     */
    @Override
    public DeviceVo queryById(Long id) {
        DeviceVo deviceVo = baseMapper.selectVoById(id);

        List<ThingsModelValueItem> list = getCacheDeviceStatus(deviceVo.getProductId(), deviceVo.getDeviceCode());
        if (list != null && !list.isEmpty()) {
            // redis中获取设备状态（物模型值）
            deviceVo.setThingsModelValue(JSONUtil.parseArray(list));
        }
        return deviceVo;
    }

    private List<ThingsModelValueItem> getCacheDeviceStatus(Long productId, String deviceNumber) {
        String key = RedisKeyBuilder.buildTSLVCacheKey(productId, deviceNumber);
        Map<String, String> map = RedisUtils.getCacheMap(key);
        List<ThingsModelValueItem> valueList;
        if (map == null || map.isEmpty()) {
            // 缓存设备状态（物模型值）到redis
            valueList = cacheDeviceStatus(productId, deviceNumber);
        } else {
            // 获取redis缓存的物模型值
            valueList = map.values().stream().map(s -> JSONUtil.toBean(s, ThingsModelValueItem.class))
                .collect(Collectors.toList());
        }
        return valueList;
    }

    /**
     * 分页查询设备管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备管理分页列表
     */
    @Override
    public TableDataInfo<DeviceVo> queryPageList(DeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Device> lqw = buildQueryWrapper(bo);
        Page<DeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备管理列表
     *
     * @param bo 查询条件
     * @return 设备管理列表
     */
    @Override
    public List<DeviceVo> queryList(DeviceBo bo) {
        LambdaQueryWrapper<Device> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Device> buildQueryWrapper(DeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Device> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Device::getId);
        lqw.eq(bo.getDeviceStatus() != null, Device::getDeviceStatus, bo.getDeviceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), Device::getDeviceCode, bo.getDeviceCode());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), Device::getDeviceName, bo.getDeviceName());
        lqw.eq(bo.getNodeId() != null, Device::getNodeId, bo.getNodeId());
        lqw.eq(StringUtils.isNotBlank(bo.getNodePath()), Device::getNodePath, bo.getNodePath());
        lqw.eq(bo.getDeviceType() != null, Device::getDeviceType, bo.getDeviceType());
        lqw.eq(bo.getProductId() != null, Device::getProductId, bo.getProductId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Device::getProductName, bo.getProductName());
        lqw.eq(bo.getEstateId() != null, Device::getEstateId, bo.getEstateId());
        lqw.eq(bo.getCreateBy() != null, Device::getCreateBy, bo.getCreateBy());
        return lqw;
    }

    /**
     * 新增设备管理
     *
     * @param bo 设备管理
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(DeviceBo bo) {
        Device add = MapstructUtils.convert(bo, Device.class);
        validEntityBeforeSave(add);


        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            cacheDeviceStatus(bo.getProductId(), bo.getDeviceCode());

            //保存到device_user_rel
            buildDeviceNodeRel(add);
        }
        return flag;
    }

    private void buildDeviceNodeRel(Device device) {
        if (device.getId() != null && device.getId() > 0) {
            deviceNodeRelMapper.delete(new LambdaQueryWrapper<DeviceNodeRel>().eq(DeviceNodeRel::getDeviceId, device.getId()));
        }

        if (device.getEstateId() == null || device.getEstateId() <= 0) {
            return;
        }

        DeviceNodeRel rel = new DeviceNodeRel();
        rel.setEstateId(device.getEstateId());
        rel.setDeviceId(device.getId());
        if (device.getNodeId() != null && device.getNodeId() > 0) {
            Node node = nodeMapper.selectById(device.getNodeId());
            if (node == null) {
                throw new ServiceException("楼栋不存在");
            }

            rel.setNodeId(node.getId());
            rel.setNodePath(node.getPath());
            rel.setEstateId(node.getEstateId());
        }

        deviceNodeRelMapper.insert(rel);
    }

    /**
     * 修改设备管理
     *
     * @param bo 设备管理
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DeviceBo bo) {
        Device update = MapstructUtils.convert(bo, Device.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag && bo.getProductId() != null) {
            cacheDeviceStatus(bo.getProductId(), bo.getDeviceCode());

            buildDeviceNodeRel(update);

        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Device entity) {

        // 设备编号唯一检查
        Device existDevice = baseMapper.selectOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceCode, entity.getDeviceCode()).last("limit 1"));
        if (existDevice != null) {
            // 检查是否是同一设备（更新操作）
            boolean isSameDevice = entity.getId() != null && existDevice.getId().equals(entity.getId());
            if (!isSameDevice) {
                throw new ServiceException("设备编号：" + entity.getDeviceCode() + " 已经存在，新增失败");
            }
        }

        Product product = productMapper.selectById(entity.getProductId());
        if (product == null) {
            throw new ServiceException("产品不存在");
        }

        entity.setProductName(product.getProductName());
        entity.setDeviceType(product.getDeviceType());
        entity.setProductSymbol(product.getProductSymbol());

        if (entity.getEstateId() != null && entity.getEstateId() > 0 && estateMapper.selectById(entity.getEstateId()) == null) {
            throw new ServiceException("小区不存在");
        }

        if (entity.getNodeId() != null && entity.getNodeId() > 0) {
            Node node = nodeMapper.selectById(entity.getNodeId());
            if (node == null) {
                throw new ServiceException("楼栋不存在");
            }
            entity.setNodePath(node.getPath());
        }

    }

    /**
     * 校验并批量删除设备管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        // 删除device_node_rel表记录
        deviceNodeRelMapper.delete(new LambdaQueryWrapper<DeviceNodeRel>().in(DeviceNodeRel::getDeviceId, ids));
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String generationDeviceCode(Integer deviceType) {

        String number = "D" + getStringRandom(19);
        Long count = baseMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getDeviceCode, number));
        if (count == 0) {
            return number;
        } else {
            generationDeviceCode(deviceType);
        }
        return "";
    }

    @Override
    public Long countByProductId(Long productId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getProductId, productId));
    }

    @Override
    public DeviceVo selectDeviceRunningStatusByDeviceId(Long id, Integer slaveId) {

        Device one = baseMapper.selectById(id);
        if (one == null) {
            throw new ServiceException("设备不存在");
        }

        return getRunningDeviceStatus(slaveId, one);

    }

    @Override
    public DeviceVo selectDeviceRunningStatusByDeviceCode(String deviceCode, Integer slaveId) {

        Device one = baseMapper.selectDeviceByDeviceCode(deviceCode);
        if (one == null) {
            throw new ServiceException("设备不存在");
        }

        return getRunningDeviceStatus(slaveId, one);

    }

    private DeviceVo getRunningDeviceStatus(Integer slaveId, Device one) {
        DeviceVo device = MapstructUtils.convert(one, DeviceVo.class);

        JSONObject thingsModelObject = JSONUtil.parseObj(thingsModelService.getCacheThingsModelByProductId(device.getProductId()));
        JSONArray properties = thingsModelObject.getJSONArray("properties");
        JSONArray functions = thingsModelObject.getJSONArray("functions");
        List<ThingsModelValueItem> thingsModelValueItems = getCacheDeviceStatus(device.getProductId(), device.getDeviceCode());
        // 物模型转换赋值
        List<ThingsModelItem> thingsList = new ArrayList<>();
        //判断一下properties 和 functions是否为空, 否则报空指针
        if (!CollectionUtils.isEmpty(properties)) {
            thingsList.addAll(convertJsonToThingsList(properties, thingsModelValueItems, 1));
        }
        if (!CollectionUtils.isEmpty(functions)) {
            List<ThingsModelItem> items = convertJsonToThingsList(functions, thingsModelValueItems, 2);
            thingsList.addAll(items);
        }
        // 排序
        thingsList = thingsList.stream()
            .filter(model -> {
                if (null != slaveId) {
                    return model.getSlaveId().intValue() == slaveId.intValue();
                }
                return true;
            })
            .sorted(Comparator.comparing(ThingsModelItem::getOrder).reversed()).collect(Collectors.toList());
        device.setThingsModels(thingsList);
        return device;
    }

    @Override
    public DeviceVo selectDeviceByDeviceCode(String deviceCode) {
        Device device = baseMapper.selectDeviceByDeviceCode(deviceCode);
        return MapstructUtils.convert(device, DeviceVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int reportDevice(Device device, DeviceVo deviceVo) {
        // 未采用设备定位则清空定位，定位方式(1=ip自动定位，2=设备定位，3=自定义)
        if (deviceVo.getLocationWay() != 2) {
            device.setLatitude(null);
            device.setLongitude(null);
        }
        int result = 0;
//        if (deviceVo != null && device.getUserId() != null) {
//            // 通过配网或者扫码关联设备后，设备的用户信息需要变更
//            if (deviceVo.getUserId().longValue() != device.getUserId().longValue()) {
//                // 先删除设备的所有用户
//                deviceUserMapper.deleteDeviceUserByDeviceId(new UserIdDeviceIdModel(null, deviceVo.getDeviceId()));
//                // 添加新的设备用户
//                SysUser sysUser = userService.selectUserById(device.getUserId());
//                DeviceUser deviceUser = new DeviceUser();
//                deviceUser.setUserId(sysUser.getUserId());
//                deviceUser.setUserName(sysUser.getUserName());
//                deviceUser.setPhonenumber(sysUser.getPhonenumber());
//                deviceUser.setDeviceId(deviceVo.getDeviceId());
//                deviceUser.setDeviceName(deviceVo.getDeviceName());
//                deviceUser.setTenantId(deviceVo.getTenantId());
//                deviceUser.setTenantName(deviceVo.getTenantName());
//                deviceUser.setIsOwner(1);
//                deviceUser.setCreateTime(DateUtils.getNowDate());
//                deviceUserMapper.insertDeviceUser(deviceUser);
//                // 更新设备用户信息
//                device.setUserId(device.getUserId());
//                device.setUserName(sysUser.getUserName());
//            }
//            device.setUpdateTime(DateUtils.getNowDate());
//            if (deviceVo.getActiveTime() == null || deviceVo.getActiveTime().equals("")) {
//                device.setActiveTime(DateUtils.getNowDate());
//            }
//            // 不更新物模型
//            device.setThingsModelValue(null);
//            result = deviceMapper.updateDeviceBySerialNumber(device);
//        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ThingsModelSimpleItem> reportDeviceThingsModelValue(ThingsModelValuesInput input, int type, boolean isShadow) {
        String key = RedisKeyBuilder.buildTSLVCacheKey(input.getProductId(), input.getDeviceNumber());
        Map<String, String> maps = new HashMap<>();
        List<ThingsModelSimpleItem> list = new ArrayList<>();
        for (ThingsModelSimpleItem item : input.getThingsModelSimpleItem()) {
            String identity = item.getId();
            Integer slaveId = input.getSlaveId() == null ? item.getSlaveId() : input.getSlaveId();
            String deviceCode = slaveId == null ? input.getDeviceNumber() : input.getDeviceNumber() + "_" + slaveId;
            if (identity.startsWith("array_")) {
                identity = identity.substring(9);
            }
            // 查询redis中物模型
            identity = identity + (slaveId != null ? "#" + slaveId : "");
            PropertyDto dto = thingsModelService.getSingleThingModels(input.getProductId(), identity);
            if (null == dto || dto.getId() == null) {
                continue;
            }
            String id = item.getId();
            Object value = TypeUtils.convert(dto.getDatatype().getStr("type"), item.getValue());


            /* ★★★★★★★★★★★★★★★★★★★★★★  处理数据 - 开始 ★★★★★★★★★★★★★★★★★★★★★★*/
            String cacheValue = RedisUtils.getCacheMapValue(key, identity);
            ValueItem valueItem = JSONUtil.toBean(cacheValue, ValueItem.class);
            if (null == valueItem) {
                valueItem = new ValueItem(id, dto.getTempSlaveId(), id);
            }
            if (id.startsWith("array_")) {
                // 查询是否有缓存，如果没有先进行缓存
                if (!RedisUtils.hasKey(key)) {
                    DeviceVo device = this.selectDeviceByDeviceCode(input.getDeviceNumber());
                    this.queryById(device.getId());
                }

                int index = Integer.parseInt(id.substring(6, 8));
                if (isShadow) {
                    String[] shadows = String.valueOf(valueItem.getShadow()).split(",");
                    shadows[index] = String.valueOf(value);
                    valueItem.setShadow(String.join(",", shadows));
                } else {
                    // 设置值，获取数组值，然后替换其中元素
                    valueItem.setTs(DateUtils.getNowDate());
                    String[] values = String.valueOf(valueItem.getValue()).split(",");
                    values[index] = String.valueOf(value);
                    valueItem.setValue(String.join(",", values));

                    String[] shadows = String.valueOf(valueItem.getShadow()).split(",");
                    shadows[index] = String.valueOf(value);
                    valueItem.setShadow(String.join(",", shadows));
                }
                RedisUtils.setCacheMapValue(key, identity, JSONUtil.toJsonStr(valueItem));
                //maps.put(identity, JSONObject.toJSONString(valueItem));
            } else {
                if (isShadow) {
                    valueItem.setShadow(value);
                } else {
                    valueItem.setValue(value);
                    valueItem.setShadow(value);
                    valueItem.setTs(DateUtils.getNowDate());
                }
                maps.put(identity, JSONUtil.toJsonStr(valueItem));
            }
            /* ★★★★★★★★★★★★★★★★★★★★★★  处理数据 - 结束 ★★★★★★★★★★★★★★★★★★★★★★*/

            /*★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 开始 ★★★★★★★★★★★★★★★★★★★★★★*/
            if (dto.getIsHistory() != null && dto.getIsHistory()) {

            }
            list.add(item);
        }
        RedisUtils.setCacheMap(key, maps);
        /* ★★★★★★★★★★★★★★★★★★★★★★  存储数据 - 结束 ★★★★★★★★★★★★★★★★★★★★★★*/
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDeviceStatusAndLocation(DeviceVo deviceVo, String ipAddress, boolean insertEvent) {
        // 设置自动定位和状态
        if (StringUtils.isNotEmpty(ipAddress)) {
            if (deviceVo.getActiveTime() == null) {
                deviceVo.setActiveTime(DateUtils.getNowDate());
            }
            // 定位方式(1=ip自动定位，2=设备定位，3=自定义)
            if (deviceVo.getLocationWay() == 1) {
                deviceVo.setNetworkIp(ipAddress);
                //setLocation(ipAddress, deviceVo);
            }
        }
        Device convert = MapstructUtils.convert(deviceVo, Device.class);
        int result = baseMapper.updateById(convert);
        // 添加到设备日志
        if (insertEvent) {
            EventLog event = new EventLog();
            event.setDeviceId(deviceVo.getId());
            event.setDeviceName(deviceVo.getDeviceName());
            event.setDeviceCode(deviceVo.getDeviceCode());
            event.setIsMonitor(false);
//        event.setUserId(deviceVo.getUserId());
//        event.setUserName(deviceVo.getUserName());
//        event.setTenantId(deviceVo.getTenantId());
//        event.setTenantName(deviceVo.getTenantName());
            event.setCreateTime(DateUtils.getNowDate());
            // 日志模式 1=影子模式，2=在线模式，3=其他
            event.setMode(3);
            if (deviceVo.getDeviceStatus() == 3) {
                event.setLogValue("1");
                event.setRemark("设备上线");
                event.setIdentity("online");
                event.setLogType(5);
            } else if (deviceVo.getDeviceStatus() == 4) {
                event.setLogValue("0");
                event.setRemark("设备离线");
                event.setIdentity("offline");
                event.setLogType(6);
            }
            eventLogMapper.insert(event);
        }
        return result;
    }

    /**
     * 物模型基本类型转换赋值
     *
     * @param jsonArray
     * @param thingsModelValues
     * @param type
     * @return
     */
    @Async
    public List<ThingsModelItem> convertJsonToThingsList(JSONArray jsonArray, List<ThingsModelValueItem> thingsModelValues, Integer type) {
        List<ThingsModelItem> thingsModelList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ThingsModelItem thingsModel = new ThingsModelItem();
            JSONObject thingsJson = jsonArray.getJSONObject(i);
            JSONObject datatypeJson = thingsJson.getJSONObject("datatype");
            thingsModel.setId(thingsJson.getStr("id"));
            thingsModel.setName(thingsJson.getStr("name"));
            thingsModel.setIsMonitor(thingsJson.getBool("isMonitor", false));
            thingsModel.setIsReadonly(thingsJson.getBool("isReadonly", false));
            thingsModel.setIsChart(thingsJson.getBool("isChart", false));
            thingsModel.setIsSharePerm(thingsJson.getBool("isSharePerm", false));
            thingsModel.setIsHistory(thingsJson.getBool("isHistory", false));
            thingsModel.setOrder(thingsJson.getInt("order") == null ? 0 : thingsJson.getInt("order"));
            thingsModel.setType(type);
            thingsModel.setSlaveId(thingsJson.getInt("tempSlaveId"));
            thingsModel.setRegId(thingsJson.getStr("regId"));
            // 获取value
            for (ThingsModelValueItem valueItem : thingsModelValues) {
                if (valueItem.getId() == null) {
                    continue;
                }
                if (valueItem.getId().equals(thingsModel.getId()) || valueItem.getId().equals(thingsModel.getRegId())) {
                    Object valueObject = TypeUtils.convert(datatypeJson.getStr("type"), valueItem.getValue());
                    Object shadowObject = TypeUtils.convert(datatypeJson.getStr("type"), valueItem.getShadow());

                    thingsModel.setValue(valueObject);
                    thingsModel.setShadow(shadowObject);
                    thingsModel.setTs(valueItem.getTs());
                    break;
                }
            }
            // json转DataType(DataType赋值)
            Datatype dataType = convertJsonToDataType(datatypeJson, thingsModelValues, type, thingsModel.getId() + "_");
            thingsModel.setDatatype(dataType);
            if (JSONUtil.isTypeJSON(Convert.toStr(thingsModel.getValue()))) {
                JSONObject jsonObject = JSONUtil.parseObj(thingsModel.getValue());
                for (EnumItem enumItem : dataType.getEnumList()) {
                    ThingsModelItem model = new ThingsModelItem();
                    BeanUtils.copyProperties(thingsModel, model);
                    String val = jsonObject.getStr(enumItem.getValue());
                    model.setValue(val);
                    model.setName(enumItem.getValue());
                    thingsModelList.add(model);
                }
            } else {
                // 物模型项添加到集合
                thingsModelList.add(thingsModel);
            }
        }
        return thingsModelList;
    }

    /**
     * 物模型DataType转换
     *
     * @param datatypeJson
     * @param thingsModelValues
     * @param type
     * @param parentIdentifier  上级标识符
     * @return
     */
    private Datatype convertJsonToDataType(JSONObject datatypeJson, List<ThingsModelValueItem> thingsModelValues, Integer type, String parentIdentifier) {
        Datatype dataType = new Datatype();
        //有些物模型数据定义为空的情况兼容
        if (datatypeJson == null) {
            return dataType;
        }
        dataType.setType(datatypeJson.getStr("type"));
        if (dataType.getType().equals("decimal")) {
            dataType.setMax(datatypeJson.getBigDecimal("max"));
            dataType.setMin(datatypeJson.getBigDecimal("min"));
            dataType.setStep(datatypeJson.getBigDecimal("step"));
            dataType.setUnit(datatypeJson.getStr("unit"));
        } else if (dataType.getType().equals("integer")) {
            dataType.setMax(datatypeJson.getBigDecimal("max"));
            dataType.setMin(datatypeJson.getBigDecimal("min"));
            dataType.setStep(datatypeJson.getBigDecimal("step"));
            dataType.setUnit(datatypeJson.getStr("unit"));
        } else if (dataType.getType().equals("bool")) {
            dataType.setFalseText(datatypeJson.getStr("falseText"));
            dataType.setTrueText(datatypeJson.getStr("trueText"));
        } else if (dataType.getType().equals("string")) {
            dataType.setMaxLength(datatypeJson.getInt("maxLength"));
        } else if (dataType.getType().equals("enum")) {
            List<EnumItem> enumItemList = JSONUtil.toList(datatypeJson.getStr("enumList"), EnumItem.class);
            dataType.setEnumList(enumItemList);
            dataType.setShowWay(datatypeJson.getStr("showWay"));
        } else if (dataType.getType().equals("object")) {
            JSONArray jsonArray = JSONUtil.parseArray(datatypeJson.getStr("params"));
            // 物模型值过滤（parentId_开头）
            thingsModelValues = thingsModelValues.stream().filter(x -> x.getId().startsWith(parentIdentifier)).collect(Collectors.toList());
            List<ThingsModelItem> thingsList = convertJsonToThingsList(jsonArray, thingsModelValues, type);
            // 排序
            thingsList = thingsList.stream().sorted(Comparator.comparing(ThingsModelItem::getOrder).reversed()).collect(Collectors.toList());
            dataType.setParams(thingsList);
        } else if (dataType.getType().equals("array")) {
            dataType.setArrayType(datatypeJson.getStr("arrayType"));
            dataType.setArrayCount(datatypeJson.getInt("arrayCount"));
            if ("object".equals(dataType.getArrayType())) {
                // 对象数组
                JSONArray jsonArray = datatypeJson.getJSONArray("params");
                // 物模型值过滤（parentId_开头）
                thingsModelValues = thingsModelValues.stream().filter(x -> x.getId().startsWith(parentIdentifier)).collect(Collectors.toList());
                List<ThingsModelItem> thingsList = convertJsonToThingsList(jsonArray, thingsModelValues, type);
                // 排序
                thingsList = thingsList.stream().sorted(Comparator.comparing(ThingsModelItem::getOrder).reversed()).toList();
                // 数组类型物模型里面对象赋值
                List<ThingsModelItem>[] arrayParams = new List[dataType.getArrayCount()];
                for (int i = 0; i < dataType.getArrayCount(); i++) {
                    List<ThingsModelItem> thingsModels = new ArrayList<>();
                    for (ThingsModelItem thingsModelItem : thingsList) {
                        ThingsModelItem thingsModel = new ThingsModelItem();
                        BeanUtils.copyProperties(thingsModelItem, thingsModel);
                        if (ObjectUtil.isNotEmpty(thingsModelItem.getShadow())) {
                            String convert = Convert.toStr(thingsModelItem.getShadow());
                            String[] shadows = convert.split(",");
                            if (i + 1 > shadows.length) {
                                // 解决产品取消发布，增加数组长度导致设备影子和值赋值失败
                                thingsModel.setShadow(" ");
                            } else {
                                thingsModel.setShadow(shadows[i]);
                            }
                        }

                        if (ObjectUtil.isNotEmpty(thingsModelItem.getValue())) {
                            String convert = Convert.toStr(thingsModelItem.getShadow());
                            String[] values = convert.split(",");
                            if (i + 1 > values.length) {
                                thingsModel.setValue(" ");
                            } else {
                                thingsModel.setValue(values[i]);
                            }
                        }

                        thingsModels.add(thingsModel);
                    }
                    arrayParams[i] = thingsModels;
                }
                dataType.setArrayParams(arrayParams);
            }
        }
        return dataType;
    }

    public String getStringRandom(int length) {
        String val = "";
        Random random = new Random();
        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                // int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + 65);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }


    /**
     * 缓存设备状态到redis
     *
     * @return
     */
    @Override
    public List<ThingsModelValueItem> cacheDeviceStatus(Long productId, String deviceCode) {
        // 获取物模型,设置默认值
        String thingsModels = thingsModelService.getCacheThingsModelByProductId(productId);
        JSONObject thingsModelObject = JSONUtil.parseObj(thingsModels);
        JSONArray properties = thingsModelObject.getJSONArray("properties");
        JSONArray functions = thingsModelObject.getJSONArray("functions");
        List<ThingsModelValueItem> valueList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(properties)) {
            valueList.addAll(properties.toList(ThingsModelValueItem.class));
        }
        if (!CollectionUtils.isEmpty(functions)) {
            valueList.addAll(functions.toList(ThingsModelValueItem.class));
        }
        String key = RedisKeyBuilder.buildTSLVCacheKey(productId, deviceCode);
        Map<String, String> maps = new HashMap<>();
        for (ThingsModelValueItem item : valueList) {
            String id = item.getTempSlaveId() == null ? item.getId() : item.getId() + "#" + item.getTempSlaveId();
            List<ThingsModelItem> params = item.getDatatype().getParams();
            ValueItem valueItem = new ValueItem();
            valueItem.setValue("");
            valueItem.setId(id);
            valueItem.setName(item.getName());
            valueItem.setShadow("");
            if (null != item.getDatatype()) {
                String type = item.getDatatype().getType();
                DataEnum dataEnum = DataEnum.convert(type);
                switch (dataEnum) {
                    case INTEGER:
                    case DECIMAL:
                    case STRING:
                    case ENUM:
                        if (null != item.getTempSlaveId()) {
                            valueItem.setSlaveId(item.getTempSlaveId());
                            valueItem.setRegArr(item.getId());
                        }
                        maps.put(id, JSONUtil.toJsonStr(valueItem));
                        break;
                    case ARRAY:
                        // 数组元素赋值（英文逗号分割的字符串,包含简单类型数组和对象类型数组，数组元素ID格式：array_01_humidity）
                        String defaultValue = " ";
                        if (item.getDatatype().getArrayType().equals("object")) {
                            for (int i = 0; i < item.getDatatype().getArrayCount(); i++) {
                                // 默认值需要保留为空格,便于解析字符串为数组
                                defaultValue = defaultValue + ", ";
                            }
                            for (ThingsModelItem param : params) {
                                valueItem.setValue(defaultValue);
                                valueItem.setShadow(defaultValue);
                                valueItem.setName(param.getName());
                                valueItem.setId(param.getId());
                                maps.put(param.getId(), JSONUtil.toJsonStr(valueItem));
                            }
                        } else {
                            for (int i = 0; i < item.getDatatype().getArrayCount(); i++) {
                                defaultValue = defaultValue + ", ";
                            }
                            valueItem.setValue(defaultValue);
                            valueItem.setShadow(defaultValue);
                            valueItem.setName(item.getName());
                            valueItem.setId(item.getId());
                            maps.put(item.getId(), JSONUtil.toJsonStr(valueItem));
                        }
                        break;
                    case OBJECT:
                        for (ThingsModelItem param : params) {
                            valueItem.setName(param.getName());
                            valueItem.setId(param.getId());
                            maps.put(param.getId(), JSONUtil.toJsonStr(valueItem));
                        }
                        break;
                }
            }
        }
        RedisUtils.setCacheMap(key, maps);
        return valueList;
    }

    @Override
    public String registerByMac(String productSymbol, String deviceMac, String deviceName) {

        Product product = productMapper.selectOne(new LambdaQueryWrapper<Product>().eq(Product::getProductSymbol, productSymbol).last("limit 1"));
        if (product == null) {
            throw new ServiceException("产品[" + productSymbol + "]不存在");
        }
        Device device = baseMapper.selectOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceMac, deviceMac).last("limit 1"));
        if (device != null) {
            return device.getDeviceCode();
        }
        String uid = generationDeviceCode(product.getDeviceType());
        device = new Device();
        device.setDeviceName(deviceName);
        device.setDeviceCode(uid);
        device.setProductId(product.getId());
        device.setProductName(product.getProductName());
        device.setCreateBy(product.getCreateBy());
        device.setDeviceType(product.getDeviceType());
        device.setActiveTime(new Date());
        device.setDeviceMac(deviceMac);
        device.setEstateId(0L);
        device.setDeviceStatus(3);
        device.setLocationWay(1);
        device.setProductSymbol(product.getProductSymbol());

        baseMapper.insert(device);
        cacheDeviceStatus(device.getProductId(), device.getDeviceCode());
        return uid;
    }

    @Override
    public Boolean updateById(Device update) {
        return baseMapper.updateById(update) > 0;
    }
}
