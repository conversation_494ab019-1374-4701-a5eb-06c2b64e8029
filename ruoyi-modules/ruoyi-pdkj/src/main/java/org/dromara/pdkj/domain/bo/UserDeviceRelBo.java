package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.UserDeviceRel;

/**
 * 用户设备业务对象 user_device_rel
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserDeviceRel.class, reverseConvertGenerate = false)
public class UserDeviceRelBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * $column.columnComment
     */
    @NotNull(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * $column.columnComment
     */
    private Long userId;

    /**
     * $column.columnComment
     */
    private Long roleType;

    /**
     * 设备code
     */
    private String deviceCode;


}
