package org.dromara.pdkj.domain.vo;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.ThingsModel;

import java.io.Serial;
import java.io.Serializable;



/**
 * 物模型视图对象 tb_things_model
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ThingsModel.class)
public class ThingsModelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物模型ID
     */
    @ExcelProperty(value = "物模型ID")
    private Long id;

    /**
     * 物模型名称
     */
    @ExcelProperty(value = "物模型名称")
    private String modelName;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 标识符
     */
    @ExcelProperty(value = "标识符")
    private String identifier;

    /**
     * 模型类别
     */
    @ExcelProperty(value = "模型类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_things_type")
    private Integer type;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_data_type")
    private String datatype;

    /**
     * 数据定义
     */
    @ExcelProperty(value = "数据定义")
    private JSONObject specs;

    /**
     * 是否图表展示
     */
    @ExcelProperty(value = "是否图表展示", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isChart;

    /**
     * 是否实时监测
     */
    @ExcelProperty(value = "是否实时监测", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isMonitor;

    /**
     * 是否历史存储
     */
    @ExcelProperty(value = "是否历史存储", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isHistory;

    /**
     * 是否只读数据
     */
    @ExcelProperty(value = "是否只读数据", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isReadonly;

    /**
     * 设备分享权限
     */
    @ExcelProperty(value = "设备分享权限", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isSharePerm;

    /**
     * 排序，值越大，排序越靠前
     */
    @ExcelProperty(value = "排序，值越大，排序越靠前")
    private Integer modelOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 从机id
     */
    @ExcelProperty(value = "从机id")
    private String tempSlaveId;

    /**
     * 计算公式
     */
    @ExcelProperty(value = "计算公式")
    private String formula;

    /**
     * 控制公式
     */
    @ExcelProperty(value = "控制公式")
    private String reverseFormula;

    /**
     * 寄存器地址值
     */
    @ExcelProperty(value = "寄存器地址值")
    private Long regAddr;

    /**
     * 位定义选项
     */
    @ExcelProperty(value = "位定义选项")
    private String bitOption;

    /**
     * 解析类型
     */
    @ExcelProperty(value = "解析类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_value_type")
    private Integer valueType;

    /**
     * 是计算参数
     */
    @ExcelProperty(value = "是计算参数", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_yes_no")
    private Boolean isParams;

    /**
     * 读取寄存器数量
     */
    @ExcelProperty(value = "读取寄存器数量")
    private Integer quantity;

    /**
     * modbus功能码
     */
    @ExcelProperty(value = "modbus功能码")
    private String code;

    /**
     * modbus解析类型
     */
    @ExcelProperty(value = "modbus解析类型")
    private String parseType;


}
