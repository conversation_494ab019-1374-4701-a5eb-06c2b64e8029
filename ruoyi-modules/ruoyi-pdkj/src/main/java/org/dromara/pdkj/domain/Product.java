package org.dromara.pdkj.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.HutoolJsonObjectTypeHandler;

import java.io.Serial;

/**
 * 产品管理对象 tb_product
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "iot.product", autoResultMap = true)
public class Product extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Integer delFlag;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String productName;

    /**
     * 产品分类
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 物模型
     */
    @TableField(typeHandler = HutoolJsonObjectTypeHandler.class)
    private JSONObject thingsModelsJson;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 联网方式
     */
    private Long networkMethod;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 产品支持的传输协议
     */
    private String transport;

    /**
     * 备注
     */
    private String remark;

    /**
     * mqtt账号
     */
    private String mqttAccount;

    /**
     * mqtt密码
     */
    private String mqttPassword;

    /**
     * 产品秘钥
     */
    private String mqttSecret;

    /**
     * 认证方式
     */
    private Long vertificateMethod;

    /**
     * 协议编号
     */
    private String protocolCode;

    private Boolean isAuthorize;

    /**
     * 产品标识
     */
    private String productSymbol;

}
