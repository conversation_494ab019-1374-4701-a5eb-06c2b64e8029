package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.pdkj.domain.DeviceNodeRel;

import java.io.Serial;
import java.io.Serializable;



/**
 * 设备楼栋关联视图对象 device_node_rel
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceNodeRel.class)
public class DeviceNodeRelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;


    /**
     * 设备code
     */
    @ExcelProperty(value = "设备code")
    private String deviceCode;

    /**
     * 楼栋ID
     */
    @ExcelProperty(value = "楼栋ID")
    private Long nodeId;

    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区ID")
    private Long estateId;

    /**
     * 楼栋路径
     */
    @ExcelProperty(value = "楼栋路径")
    private String nodePath;


}
