package org.dromara.pdkj.domain.vo;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.ThingsModelTemplate;

import java.io.Serial;
import java.io.Serializable;


/**
 * 通用物模型视图对象 tb_things_model_template
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ThingsModelTemplate.class)
public class ThingsModelTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物模型ID
     */
    @ExcelProperty(value = "物模型ID")
    private Long id;

    /**
     * 物模型名称
     */
    @ExcelProperty(value = "物模型名称")
    private String templateName;

    /**
     * 标识符
     */
    @ExcelProperty(value = "标识符")
    private String identifier;

    /**
     * 模型类别
     */
    @ExcelProperty(value = "模型类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_things_type")
    private Integer type;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_data_type")
    private String datatype;

    /**
     * 数据定义
     */
    @ExcelProperty(value = "数据定义")
    private JSONObject specs;

    /**
     * 图表展示
     */
    @ExcelProperty(value = "图表展示", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_yes_no")
    private Boolean isChart;

    /**
     * 历史存储
     */
    @ExcelProperty(value = "历史存储", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_yes_no")
    private Boolean isHistory;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer modelOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
