package org.dromara.mq.redischannel.producer;

import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.mqttclient.IEmqxMessageProducer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class EmqxMessageProducer implements IEmqxMessageProducer {
    @Override
    public void sendEmqxMessage(String topicName, DeviceReportBo deviceReportBo) {
        MessageProducer.sendOtherMsg(deviceReportBo);
    }
}
