[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for org.dromara:ruoyi-generator:jar:5.3.0
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.dromara:ruoyi-common-web:jar -> duplicate declaration of version (?) @ line 41, column 21
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] biz-smart-house-platform                                           [pom]
[INFO] ruoyi-common-core                                                  [jar]
[INFO] ruoyi-common-json                                                  [jar]
[INFO] ruoyi-common-redis                                                 [jar]
[INFO] ruoyi-common-web                                                   [jar]
[INFO] ruoyi-common-satoken                                               [jar]
[INFO] ruoyi-common-log                                                   [jar]
[INFO] ruoyi-common-mybatis                                               [jar]
[INFO] ruoyi-common-tenant                                                [jar]
[INFO] ruoyi-common-encrypt                                               [jar]
[INFO] ruoyi-common-social                                                [jar]
[INFO] ruoyi-common-sse                                                   [jar]
[INFO] ruoyi-common-oss                                                   [jar]
[INFO] ruoyi-common-translation                                           [jar]
[INFO] ruoyi-common-excel                                                 [jar]
[INFO] ruoyi-common-idempotent                                            [jar]
[INFO] ruoyi-common-sensitive                                             [jar]
[INFO] ruoyi-system                                                       [jar]
[INFO] ruoyi-pdkj                                                         [jar]
[INFO] ruoyi-common-mail                                                  [jar]
[INFO] ruoyi-common-ratelimiter                                           [jar]
[INFO] ruoyi-extend                                                       [pom]
[INFO] ruoyi-mqtt-client                                                  [jar]
[INFO] ruoyi-gateway-mq                                                   [jar]
[INFO] ruoyi-gateway-boot                                                 [jar]
[INFO] ruoyi-server-base-server                                           [jar]
[INFO] ruoyi-server-core                                                  [jar]
[INFO] ruoyi-protocol-collect                                             [jar]
[INFO] ruoyi-server-mqtt-broker                                           [jar]
[INFO] ruoyi-server-boot-strap                                            [jar]
[INFO] ruoyi-common-doc                                                   [jar]
[INFO] ruoyi-generator                                                    [jar]
[INFO] biz-smart-house-platform-api                                       [jar]
[INFO] ruoyi-common-job                                                   [jar]
[INFO] ruoyi-common-security                                              [jar]
[INFO] ruoyi-common-sms                                                   [jar]
[INFO] ruoyi-common-websocket                                             [jar]
[INFO] ruoyi-common                                                       [pom]
[INFO] ruoyi-monitor-admin                                                [jar]
[INFO] ruoyi-snailjob-server                                              [jar]
[INFO] ruoyi-job                                                          [jar]
[INFO] ruoyi-workflow                                                     [jar]
[INFO] ruoyi-modules                                                      [pom]
[INFO] ruoyi-protocol                                                     [pom]
[INFO] ruoyi-server                                                       [pom]
[INFO] ruoyi-gateway                                                      [pom]
[INFO] 
[INFO] ---------------------< org.dromara:ruoyi-vue-plus >---------------------
[INFO] Building biz-smart-house-platform 5.3.0                           [1/46]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-vue-plus ---
[INFO] org.dromara:ruoyi-vue-plus:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-common-core >--------------------
[INFO] Building ruoyi-common-core 5.3.0                                  [2/46]
[INFO]   from ruoyi-common/ruoyi-common-core/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-core ---
[INFO] org.dromara:ruoyi-common-core:jar:5.3.0
[INFO] +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] +- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] +- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO]    \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-common-json >--------------------
[INFO] Building ruoyi-common-json 5.3.0                                  [3/46]
[INFO]   from ruoyi-common/ruoyi-common-json/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-json ---
[INFO] org.dromara:ruoyi-common-json:jar:5.3.0
[INFO] +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-common-redis >-------------------
[INFO] Building ruoyi-common-redis 5.3.0                                 [4/46]
[INFO]   from ruoyi-common/ruoyi-common-redis/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-redis ---
[INFO] org.dromara:ruoyi-common-redis:jar:5.3.0
[INFO] +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] +- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-web >--------------------
[INFO] Building ruoyi-common-web 5.3.0                                   [5/46]
[INFO]   from ruoyi-common/ruoyi-common-web/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-web ---
[INFO] org.dromara:ruoyi-common-web:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] +- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |  +- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |     \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |        +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |        \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-common-satoken >------------------
[INFO] Building ruoyi-common-satoken 5.3.0                               [6/46]
[INFO]   from ruoyi-common/ruoyi-common-satoken/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-satoken ---
[INFO] org.dromara:ruoyi-common-satoken:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] +- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |     +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |     |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |     \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-log >--------------------
[INFO] Building ruoyi-common-log 5.3.0                                   [7/46]
[INFO]   from ruoyi-common/ruoyi-common-log/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-log ---
[INFO] org.dromara:ruoyi-common-log:jar:5.3.0
[INFO] +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  +- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  |  \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |     +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |     |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |     \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-common-mybatis >------------------
[INFO] Building ruoyi-common-mybatis 5.3.0                               [8/46]
[INFO]   from ruoyi-common/ruoyi-common-mybatis/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-mybatis ---
[INFO] org.dromara:ruoyi-common-mybatis:jar:5.3.0
[INFO] +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |        +- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |     +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |     |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |     \- org.springframework:spring-**********************
[INFO] |        \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  +- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  |  \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |     +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |     |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |     \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] +- p6spy:p6spy:jar:3.9.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-common-tenant >-------------------
[INFO] Building ruoyi-common-tenant 5.3.0                                [9/46]
[INFO]   from ruoyi-common/ruoyi-common-tenant/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-tenant ---
[INFO] org.dromara:ruoyi-common-tenant:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile (optional)
[INFO] |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile (optional)
[INFO] |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile (optional)
[INFO] |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile (optional)
[INFO] |  |        +- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile (optional)
[INFO] |  |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile (optional)
[INFO] |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile (optional)
[INFO] |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile (optional)
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile (optional)
[INFO] |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-********************** (optional)
[INFO] |  |     +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |     |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |     |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |     |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |     |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |     |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |     |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |     |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |     |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile (optional)
[INFO] |  |     \- org.springframework:spring-********************** (optional)
[INFO] |  |        \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile (optional)
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile (optional)
[INFO] |  |  \- org.mybatis:mybatis:jar:3.5.16:compile (optional)
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile (optional)
[INFO] |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile (optional)
[INFO] |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile (optional)
[INFO] |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile (optional)
[INFO] |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile (optional)
[INFO] |  \- p6spy:p6spy:jar:3.9.1:compile (optional)
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |     +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |        +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |        |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |        \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-common-encrypt >------------------
[INFO] Building ruoyi-common-encrypt 5.3.0                              [10/46]
[INFO]   from ruoyi-common/ruoyi-common-encrypt/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-encrypt ---
[INFO] org.dromara:ruoyi-common-encrypt:jar:5.3.0
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] +- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] +- org.bouncycastle:bcprov-jdk15on:jar:1.70:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-common-social >-------------------
[INFO] Building ruoyi-common-social 5.3.0                               [11/46]
[INFO]   from ruoyi-common/ruoyi-common-social/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-social ---
[INFO] org.dromara:ruoyi-common-social:jar:5.3.0
[INFO] +- me.zhyd.oauth:JustAuth:jar:1.16.7:compile
[INFO] |  +- com.xkcoding.http:simple-http:jar:1.0.5:compile
[INFO] |  \- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-sse >--------------------
[INFO] Building ruoyi-common-sse 5.3.0                                  [12/46]
[INFO]   from ruoyi-common/ruoyi-common-sse/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-sse ---
[INFO] org.dromara:ruoyi-common-sse:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |     +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |        +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |        |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |        \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-oss >--------------------
[INFO] Building ruoyi-common-oss 5.3.0                                  [13/46]
[INFO]   from ruoyi-common/ruoyi-common-oss/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-oss ---
[INFO] org.dromara:ruoyi-common-oss:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] +- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-common-translation >----------------
[INFO] Building ruoyi-common-translation 5.3.0                          [14/46]
[INFO]   from ruoyi-common/ruoyi-common-translation/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-translation ---
[INFO] org.dromara:ruoyi-common-translation:jar:5.3.0
[INFO] +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-common-excel >-------------------
[INFO] Building ruoyi-common-excel 5.3.0                                [15/46]
[INFO]   from ruoyi-common/ruoyi-common-excel/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-excel ---
[INFO] org.dromara:ruoyi-common-excel:jar:5.3.0
[INFO] +- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |  +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |  |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |  |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |  |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |  |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |  |  \- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |     +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |     \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |        \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |           +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |           +- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |           +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |           +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |           \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO]    \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-common-idempotent >-----------------
[INFO] Building ruoyi-common-idempotent 5.3.0                           [16/46]
[INFO]   from ruoyi-common/ruoyi-common-idempotent/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-idempotent ---
[INFO] org.dromara:ruoyi-common-idempotent:jar:5.3.0
[INFO] +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |     \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] +- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------< org.dromara:ruoyi-common-sensitive >-----------------
[INFO] Building ruoyi-common-sensitive 5.3.0                            [17/46]
[INFO]   from ruoyi-common/ruoyi-common-sensitive/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-sensitive ---
[INFO] org.dromara:ruoyi-common-sensitive:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------------< org.dromara:ruoyi-system >----------------------
[INFO] Building ruoyi-system 5.3.0                                      [18/46]
[INFO]   from ruoyi-modules/ruoyi-system/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-system ---
[INFO] org.dromara:ruoyi-system:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |     \- org.springframework:spring-**********************
[INFO] |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  |  +- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |  |  \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |  |     +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |  |     |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |  |     \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  |  \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |     +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |     \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] +- org.dromara:ruoyi-common-oss:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |  |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |  |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] |  \- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] +- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- org.dromara:ruoyi-common-tenant:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |     +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |     |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |     |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |     |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |     |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |     |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |     |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |     |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |     |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |     |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |     |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |     |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |     |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |     |  \- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |     |     \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |     |        \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |     |           +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |     |           +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |     |           \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |     \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |     \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  \- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  \- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |     +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |     \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |     +- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |  \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.dromara:ruoyi-common-sensitive:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-common-encrypt:jar:5.3.0:compile
[INFO] |  +- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  +- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  \- org.bouncycastle:bcprov-jdk15on:jar:1.70:compile
[INFO] +- org.dromara:ruoyi-common-sse:jar:5.3.0:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------------< org.dromara:ruoyi-pdkj >-----------------------
[INFO] Building ruoyi-pdkj 5.3.0                                        [19/46]
[INFO]   from ruoyi-modules/ruoyi-pdkj/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-pdkj ---
[INFO] org.dromara:ruoyi-pdkj:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  \- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |  +- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |     \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |        +- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |        +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |        +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |        \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |     \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  +- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  \- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |     +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |     +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |     |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |     |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |     |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |     |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |     |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |     |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |     |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |     |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |     |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |     |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |     |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |     |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |     \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |        \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  \- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |     +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |     |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |     |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |     \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |        \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |           +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |           |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |           \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |     \- org.springframework:spring-**********************
[INFO] |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  \- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |     +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |     \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-common-mail >--------------------
[INFO] Building ruoyi-common-mail 5.3.0                                 [20/46]
[INFO]   from ruoyi-common/ruoyi-common-mail/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-mail ---
[INFO] org.dromara:ruoyi-common-mail:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] +- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-common-ratelimiter >----------------
[INFO] Building ruoyi-common-ratelimiter 5.3.0                          [21/46]
[INFO]   from ruoyi-common/ruoyi-common-ratelimiter/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-ratelimiter ---
[INFO] org.dromara:ruoyi-common-ratelimiter:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------------< org.dromara:ruoyi-extend >----------------------
[INFO] Building ruoyi-extend 5.3.0                                      [22/46]
[INFO]   from ruoyi-extend/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-extend ---
[INFO] org.dromara:ruoyi-extend:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-mqtt-client >--------------------
[INFO] Building ruoyi-mqtt-client 5.3.0                                 [23/46]
[INFO]   from ruoyi-extend/ruoyi-mqtt-client/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-mqtt-client ---
[INFO] org.dromara:ruoyi-mqtt-client:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-gateway-mq >--------------------
[INFO] Building ruoyi-gateway-mq 5.3.0                                  [24/46]
[INFO]   from ruoyi-gateway/ruoyi-gateway-mq/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-gateway-mq ---
[INFO] org.dromara:ruoyi-gateway-mq:jar:5.3.0
[INFO] +- org.dromara:ruoyi-mqtt-client:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] |  \- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |     +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |     +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |     |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |     |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |     |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |     +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |     |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |     |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |     |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |     |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |     |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |     |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |     |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |     |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |     |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |     |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |     |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |     |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |     |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |     |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |     |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |     |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |     |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |     |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |     |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |     |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |     |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |     \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |        \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-gateway-boot >-------------------
[INFO] Building ruoyi-gateway-boot 5.3.0                                [25/46]
[INFO]   from ruoyi-gateway/ruoyi-gateway-boot/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-gateway-boot ---
[INFO] org.dromara:ruoyi-gateway-boot:jar:5.3.0
[INFO] +- org.dromara:ruoyi-mqtt-client:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] |  \- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |     +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |     +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |     |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |     |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |     |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |     +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |     |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |     |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |     |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |     |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |     |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |     |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |     |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |     |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |     |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |     |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |     |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |     |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |     |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |     |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |     |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |     |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |     |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |     |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |     |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |     |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |     |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |     \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |        \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-gateway-mq:jar:5.3.0:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-server-base-server >----------------
[INFO] Building ruoyi-server-base-server 5.3.0                          [26/46]
[INFO]   from ruoyi-server/ruoyi-server-base-server/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-server-base-server ---
[INFO] org.dromara:ruoyi-server-base-server:jar:5.3.0
[INFO] +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -------------------< org.dromara:ruoyi-server-core >--------------------
[INFO] Building ruoyi-server-core 5.3.0                                 [27/46]
[INFO]   from ruoyi-server/ruoyi-server-core/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-server-core ---
[INFO] org.dromara:ruoyi-server-core:jar:5.3.0
[INFO] +- org.dromara:ruoyi-server-base-server:jar:5.3.0:compile
[INFO] |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |     |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------< org.dromara:ruoyi-protocol-collect >-----------------
[INFO] Building ruoyi-protocol-collect 5.3.0                            [28/46]
[INFO]   from ruoyi-protocol/ruoyi-protocol-collect/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-protocol-collect ---
[INFO] org.dromara:ruoyi-protocol-collect:jar:5.3.0
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- org.dromara:ruoyi-pdkj:jar:5.3.0:compile
[INFO] |  +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |  |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |  |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |  |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |  |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |  |  +- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |  |     \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |  |        +- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |        +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |  |        +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |  |        \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |  +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  |  \- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |     +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |     |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |     |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  |     \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |        \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |           +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |           |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |           \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |     \- org.springframework:spring-**********************
[INFO] |  |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] |  +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  |  \- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |     +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |     \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |  |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |  |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |  |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |  |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  \- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-common-oss:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |  |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |  |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] |  +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] |  \- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-server-mqtt-broker >----------------
[INFO] Building ruoyi-server-mqtt-broker 5.3.0                          [29/46]
[INFO]   from ruoyi-server/ruoyi-server-mqtt-broker/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-server-mqtt-broker ---
[INFO] org.dromara:ruoyi-server-mqtt-broker:jar:5.3.0
[INFO] +- org.dromara:ruoyi-server-base-server:jar:5.3.0:compile
[INFO] |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  \- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-mqtt-client:jar:5.3.0:compile
[INFO] |  +- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] |  \- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |     +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |     +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |     +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |     |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |     |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |     |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |     |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |     |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |     |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |     |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |     |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |     |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |     |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |     \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |        \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-pdkj:jar:5.3.0:compile
[INFO] |  +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |  |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |  |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |  |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |  |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |  |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |  |  +- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |  |     \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |  |        +- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |        +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |  |        +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |  |        \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |  +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  |  \- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |     +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |     |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |     |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  |     \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |        \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |           +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |           |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |           \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |     \- org.springframework:spring-**********************
[INFO] |  |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] |  +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  |  \- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |     +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |     \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |  |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |  |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |  |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |  |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  \- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-protocol-collect:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  \- org.dromara:ruoyi-common-oss:jar:5.3.0:compile
[INFO] |     +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |     |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |     |  |  \- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |     |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |     |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |     |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |     |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |     |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |     |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] |     +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] |     \- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] +- org.dromara:ruoyi-gateway-mq:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-server-core:jar:5.3.0:compile
[INFO] +- io.jsonwebtoken:jjwt:jar:0.9.1:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------< org.dromara:ruoyi-server-boot-strap >-----------------
[INFO] Building ruoyi-server-boot-strap 5.3.0                           [30/46]
[INFO]   from ruoyi-server/ruoyi-server-boot-strap/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-server-boot-strap ---
[INFO] org.dromara:ruoyi-server-boot-strap:jar:5.3.0
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-server-base-server:jar:5.3.0:compile
[INFO] |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  \- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |     |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-server-core:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-server-mqtt-broker:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-mqtt-client:jar:5.3.0:compile
[INFO] |  |  +- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] |  |  \- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |     +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |     |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |     |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  |     +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |     +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |     |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |     |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |     |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |     |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |     |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |     |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |     |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |     |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |     |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |     |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |     |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |     \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |        \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- org.dromara:ruoyi-pdkj:jar:5.3.0:compile
[INFO] |  |  +- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |  |  |  +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  |  |  +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |  |  |  |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  |  |  |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  |  |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  |  |  |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |  |  |  |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |  |  |  |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |  |  |  |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |  |  |  |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |  |  |  +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |  |  |  +- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |  |  |     \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |  |  |        +- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |  |        +- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |  |  |        +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |  |  |        \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  |  +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  |  |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |  |  +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  |  |  \- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |  |     +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  |     |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  |     |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  |  |     \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |  |        \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |  |           +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |  |           |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |  |           \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  |  +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  |  |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |  |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |  |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |  |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |  |     \- org.springframework:spring-**********************
[INFO] |  |  |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  |  |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  |  |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  |  |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] |  |  +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  |  |  \- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |  |     +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |  |     \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |  |  |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |  |  |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |  |  |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |  |  |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  |  \- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-protocol-collect:jar:5.3.0:compile
[INFO] |  |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- org.dromara:ruoyi-common-oss:jar:5.3.0:compile
[INFO] |  |     +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |  |     |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |  |     |  |  \- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |  |     |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |  |     |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |  |     |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |  |     |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |  |     |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |  |     |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] |  |     +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] |  |     \- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] |  +- org.dromara:ruoyi-gateway-mq:jar:5.3.0:compile
[INFO] |  \- io.jsonwebtoken:jjwt:jar:0.9.1:compile
[INFO] |     \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-doc >--------------------
[INFO] Building ruoyi-common-doc 5.3.0                                  [31/46]
[INFO]   from ruoyi-common/ruoyi-common-doc/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-doc ---
[INFO] org.dromara:ruoyi-common-doc:jar:5.3.0
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.springdoc:springdoc-openapi-starter-webmvc-api:jar:2.8.4:compile
[INFO] |  +- org.springdoc:springdoc-openapi-starter-common:jar:2.8.4:compile
[INFO] |  |  \- io.swagger.core.v3:swagger-core-jakarta:jar:2.2.28:compile
[INFO] |  |     +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |     +- io.swagger.core.v3:swagger-annotations-jakarta:jar:2.2.28:compile
[INFO] |  |     +- io.swagger.core.v3:swagger-models-jakarta:jar:2.2.28:compile
[INFO] |  |     +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  |     |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  |     +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |     \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- com.github.therapi:therapi-runtime-javadoc:jar:0.15.0:compile
[INFO] +- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-reflect:jar:1.9.25:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |        \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-generator >---------------------
[INFO] Building ruoyi-generator 5.3.0                                   [32/46]
[INFO]   from ruoyi-modules/ruoyi-generator/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-generator ---
[INFO] org.dromara:ruoyi-generator:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |     +- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |  \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-doc:jar:5.3.0:compile
[INFO] |  +- org.springdoc:springdoc-openapi-starter-webmvc-api:jar:2.8.4:compile
[INFO] |  |  \- org.springdoc:springdoc-openapi-starter-common:jar:2.8.4:compile
[INFO] |  |     \- io.swagger.core.v3:swagger-core-jakarta:jar:2.2.28:compile
[INFO] |  |        +- io.swagger.core.v3:swagger-annotations-jakarta:jar:2.2.28:compile
[INFO] |  |        +- io.swagger.core.v3:swagger-models-jakarta:jar:2.2.28:compile
[INFO] |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  +- com.github.therapi:therapi-runtime-javadoc:jar:0.15.0:compile
[INFO] |  \- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.18.2:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-reflect:jar:1.9.25:compile
[INFO] |        \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |           \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |     \- org.springframework:spring-**********************
[INFO] |  |        \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |     +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |        +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |        |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |        \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |     \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] +- org.apache.velocity:velocity-engine-core:jar:2.3:compile
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- org.anyline:anyline-environment-spring-data-*******************************
[INFO] |  +- org.anyline:anyline-data-*******************************
[INFO] |  |  \- org.anyline:anyline-data:jar:8.7.2-20250101:compile
[INFO] |  |     +- org.anyline:anyline-core:jar:8.7.2-20250101:compile
[INFO] |  |     |  +- org.dom4j:dom4j:jar:2.1.4:compile
[INFO] |  |     |  +- org.anyline:anyline-oro:jar:8.7.2-20250101:compile
[INFO] |  |     |  \- ognl:ognl:jar:3.2.10:compile
[INFO] |  |     |     \- org.javassist:javassist:jar:3.24.1-GA:compile
[INFO] |  |     \- org.anyline:anyline-log:jar:8.7.2-20250101:compile
[INFO] |  \- org.anyline:anyline-environment-spring-data:jar:8.7.2-20250101:compile
[INFO] |     \- org.anyline:anyline-environment-spring:jar:8.7.2-20250101:compile
[INFO] +- org.anyline:anyline-data-jdbc-postgresql:jar:8.7.2-20250101:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------< org.dromara:biz-smart-house-platform-api >--------------
[INFO] Building biz-smart-house-platform-api 5.3.0                      [33/46]
[INFO]   from ruoyi-admin/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ biz-smart-house-platform-api ---
[INFO] org.dromara:biz-smart-house-platform-api:jar:5.3.0
[INFO] +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |     \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |     +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |     \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  \- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |     +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |     \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |     \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] +- org.dromara:ruoyi-common-tenant:jar:5.3.0:compile
[INFO] +- me.zhyd.oauth:JustAuth:jar:1.16.7:compile
[INFO] |  +- com.xkcoding.http:simple-http:jar:1.0.5:compile
[INFO] |  \- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.dromara:ruoyi-common-encrypt:jar:5.3.0:compile
[INFO] |  +- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  +- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  \- org.bouncycastle:bcprov-jdk15on:jar:1.70:compile
[INFO] +- org.dromara:ruoyi-common-social:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-common-sse:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-system:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |     \- org.springframework:spring-**********************
[INFO] |  |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] |  +- org.dromara:ruoyi-common-oss:jar:5.3.0:compile
[INFO] |  |  +- software.amazon.awssdk:s3:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:aws-xml-protocol:jar:2.28.22:compile
[INFO] |  |  |  |  \- software.amazon.awssdk:aws-query-protocol:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:protocol-core:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:arns:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:profiles:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:crt-core:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-auth:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:identity-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-auth-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-auth-aws:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:checksums:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:checksums-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:retries-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:sdk-core:jar:2.28.22:compile
[INFO] |  |  |  |  \- software.amazon.awssdk:retries:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:auth:jar:2.28.22:compile
[INFO] |  |  |  |  +- software.amazon.awssdk:http-auth-aws-eventstream:jar:2.28.22:compile
[INFO] |  |  |  |  \- software.amazon.eventstream:eventstream:jar:1.0.1:compile
[INFO] |  |  |  +- software.amazon.awssdk:http-client-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:regions:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:annotations:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:utils:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:aws-core:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:metrics-spi:jar:2.28.22:compile
[INFO] |  |  |  +- software.amazon.awssdk:json-utils:jar:2.28.22:compile
[INFO] |  |  |  |  \- software.amazon.awssdk:third-party-jackson-core:jar:2.28.22:compile
[INFO] |  |  |  \- software.amazon.awssdk:endpoints-spi:jar:2.28.22:compile
[INFO] |  |  +- software.amazon.awssdk.crt:aws-crt:jar:0.31.3:compile
[INFO] |  |  \- software.amazon.awssdk:s3-transfer-manager:jar:2.28.22:compile
[INFO] |  +- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |  +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  \- org.dromara:ruoyi-common-sensitive:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-pdkj:jar:5.3.0:compile
[INFO] |  \- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |     +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |     +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |     |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |     |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |     |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |     |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |     |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |     |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |     |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |     |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |     |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |     +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |     +- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |     |  \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |     |     \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |     |        +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |     |        \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |     \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- org.dromara:ruoyi-common-mail:jar:5.3.0:compile
[INFO] |  +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  \- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |     \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] +- org.dromara:ruoyi-common-ratelimiter:jar:5.3.0:compile
[INFO] +- org.dromara.sms4j:sms4j-api:jar:3.3.3:compile
[INFO] |  \- org.dromara.sms4j:sms4j-comm:jar:3.3.3:compile
[INFO] |     +- cn.hutool:hutool-json:jar:5.8.28:compile
[INFO] |     |  \- cn.hutool:hutool-core:jar:5.8.28:compile
[INFO] |     \- cn.hutool:hutool-http:jar:5.8.28:compile
[INFO] +- org.dromara.sms4j:sms4j-core:jar:3.3.3:compile
[INFO] |  \- org.dromara.sms4j:sms4j-provider:jar:3.3.3:compile
[INFO] |     +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] |     +- com.sun.xml.bind:jaxb-impl:jar:4.0.5:compile
[INFO] |     +- com.sun.xml.bind:jaxb-core:jar:4.0.5:compile
[INFO] |     +- javax.activation:activation:jar:1.1.1:compile
[INFO] |     \- cn.hutool:hutool-crypto:jar:5.8.28:compile
[INFO] +- de.codecentric:spring-boot-admin-starter-client:jar:3.4.1:compile
[INFO] |  \- de.codecentric:spring-boot-admin-client:jar:3.4.1:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:3.4.2:test
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:3.4.2:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:3.4.2:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.9.0:test
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  +- net.minidev:json-smart:jar:2.5.1:test
[INFO] |  |  \- net.minidev:accessors-smart:jar:2.5.1:test
[INFO] |  |     \- org.ow2.asm:asm:jar:9.6:test
[INFO] |  +- org.assertj:assertj-core:jar:3.26.3:test
[INFO] |  |  \- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  +- org.awaitility:awaitility:jar:4.2.2:test
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:test
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.11.4:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-params:jar:5.11.4:test
[INFO] |  +- org.mockito:mockito-core:jar:5.14.2:test
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.15.11:test
[INFO] |  |  \- org.objenesis:objenesis:jar:3.3:compile
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:5.14.2:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.3:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- org.springframework:spring-test:jar:6.2.2:test
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.10.0:test
[INFO] +- org.postgresql:postgresql:jar:42.7.5:compile
[INFO] |  \- org.checkerframework:checker-qual:jar:3.48.3:compile
[INFO] +- org.dromara:ruoyi-gateway-boot:jar:5.3.0:compile
[INFO] |  \- org.dromara:ruoyi-mqtt-client:jar:5.3.0:compile
[INFO] |     \- org.eclipse.paho:org.eclipse.paho.client.mqttv3:jar:1.2.5:compile
[INFO] +- org.dromara:ruoyi-gateway-mq:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-server-boot-strap:jar:5.3.0:compile
[INFO] |  \- org.dromara:ruoyi-server-core:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-server-base-server:jar:5.3.0:compile
[INFO] |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  \- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] +- org.dromara:ruoyi-server-mqtt-broker:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-protocol-collect:jar:5.3.0:compile
[INFO] |  \- io.jsonwebtoken:jjwt:jar:0.9.1:compile
[INFO] +- org.springdoc:springdoc-openapi-starter-webmvc-api:jar:2.8.4:compile
[INFO] |  \- org.springdoc:springdoc-openapi-starter-common:jar:2.8.4:compile
[INFO] |     \- io.swagger.core.v3:swagger-core-jakarta:jar:2.2.28:compile
[INFO] |        +- io.swagger.core.v3:swagger-annotations-jakarta:jar:2.2.28:compile
[INFO] |        \- io.swagger.core.v3:swagger-models-jakarta:jar:2.2.28:compile
[INFO] +- org.springdoc:springdoc-openapi-starter-webmvc-ui:jar:2.8.4:compile
[INFO] |  +- org.webjars:swagger-ui:jar:5.18.2:compile
[INFO] |  \- org.webjars:webjars-locator-lite:jar:1.0.1:compile
[INFO] |     \- org.jspecify:jspecify:jar:1.0.0:compile
[INFO] +- org.dromara:ruoyi-generator:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-doc:jar:5.3.0:compile
[INFO] |  |  +- com.github.therapi:therapi-runtime-javadoc:jar:0.15.0:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.18.2:compile
[INFO] |  |     \- org.jetbrains.kotlin:kotlin-reflect:jar:1.9.25:compile
[INFO] |  |        \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |  +- org.apache.velocity:velocity-engine-core:jar:2.3:compile
[INFO] |  +- org.anyline:anyline-environment-spring-data-*******************************
[INFO] |  |  +- org.anyline:anyline-data-*******************************
[INFO] |  |  |  \- org.anyline:anyline-data:jar:8.7.2-20250101:compile
[INFO] |  |  |     +- org.anyline:anyline-core:jar:8.7.2-20250101:compile
[INFO] |  |  |     |  +- org.dom4j:dom4j:jar:2.1.4:compile
[INFO] |  |  |     |  +- org.anyline:anyline-oro:jar:8.7.2-20250101:compile
[INFO] |  |  |     |  \- ognl:ognl:jar:3.2.10:compile
[INFO] |  |  |     |     \- org.javassist:javassist:jar:3.24.1-GA:compile
[INFO] |  |  |     \- org.anyline:anyline-log:jar:8.7.2-20250101:compile
[INFO] |  |  \- org.anyline:anyline-environment-spring-data:jar:8.7.2-20250101:compile
[INFO] |  |     \- org.anyline:anyline-environment-spring:jar:8.7.2-20250101:compile
[INFO] |  \- org.anyline:anyline-data-jdbc-postgresql:jar:8.7.2-20250101:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-job >--------------------
[INFO] Building ruoyi-common-job 5.3.0                                  [34/46]
[INFO]   from ruoyi-common/ruoyi-common-job/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-job ---
[INFO] org.dromara:ruoyi-common-job:jar:5.3.0
[INFO] +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |        +- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |        \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |           \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] +- com.aizuda:snail-job-client-starter:jar:1.3.0:compile
[INFO] |  \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] +- com.aizuda:snail-job-client-job-core:jar:1.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- com.aizuda:snail-job-common-server-api:jar:1.3.0:compile
[INFO] |  |  \- com.aizuda:snail-job-common-core:jar:1.3.0:compile
[INFO] |  |     +- cn.hutool:hutool-http:jar:5.8.32:compile
[INFO] |  |     +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  |     |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  |     +- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |  |     |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |     +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |     +- com.google.protobuf:protobuf-java:jar:3.25.3:compile
[INFO] |  |     \- com.google.api.grpc:proto-google-common-protos:jar:2.41.0:compile
[INFO] |  +- com.aizuda:snail-job-common-client-api:jar:1.3.0:compile
[INFO] |  +- com.aizuda:snail-job-client-common:jar:1.3.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |  |  +- com.aizuda:snail-job-common-log:jar:1.3.0:compile
[INFO] |  |  |  \- cn.hutool:hutool-core:jar:5.8.32:compile
[INFO] |  |  +- io.grpc:grpc-netty-shaded:jar:1.66.0:compile
[INFO] |  |  |  +- io.grpc:grpc-core:jar:1.66.0:compile
[INFO] |  |  |  |  +- com.google.code.gson:gson:jar:2.11.0:runtime
[INFO] |  |  |  |  +- com.google.android:annotations:jar:*******:runtime
[INFO] |  |  |  |  \- io.grpc:grpc-context:jar:1.66.0:runtime
[INFO] |  |  |  \- io.perfmark:perfmark-api:jar:0.27.0:runtime
[INFO] |  |  +- io.grpc:grpc-protobuf:jar:1.66.0:compile
[INFO] |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.66.0:runtime
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.66.0:compile
[INFO] |  |  +- io.grpc:grpc-api:jar:1.66.0:compile
[INFO] |  |  \- io.grpc:grpc-util:jar:1.66.0:compile
[INFO] |  |     \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.24:runtime
[INFO] |  +- com.google.guava:guava:jar:32.0.1-jre:compile
[INFO] |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.33.0:compile
[INFO] |  |  +- com.google.errorprone:error_prone_annotations:jar:2.18.0:compile
[INFO] |  |  \- com.google.j2objc:j2objc-annotations:jar:2.8:compile
[INFO] |  +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------< org.dromara:ruoyi-common-security >------------------
[INFO] Building ruoyi-common-security 5.3.0                             [35/46]
[INFO]   from ruoyi-common/ruoyi-common-security/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-security ---
[INFO] org.dromara:ruoyi-common-security:jar:5.3.0
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |  \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |     +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |     |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |     \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |     +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |     \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] --------------------< org.dromara:ruoyi-common-sms >--------------------
[INFO] Building ruoyi-common-sms 5.3.0                                  [36/46]
[INFO]   from ruoyi-common/ruoyi-common-sms/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-sms ---
[INFO] org.dromara:ruoyi-common-sms:jar:5.3.0
[INFO] +- org.dromara.sms4j:sms4j-spring-boot-starter:jar:3.3.3:compile
[INFO] |  +- org.dromara.sms4j:sms4j-core:jar:3.3.3:compile
[INFO] |  |  \- org.dromara.sms4j:sms4j-provider:jar:3.3.3:compile
[INFO] |  |     +- org.dromara.sms4j:sms4j-api:jar:3.3.3:compile
[INFO] |  |     |  \- org.dromara.sms4j:sms4j-comm:jar:3.3.3:compile
[INFO] |  |     |     +- cn.hutool:hutool-json:jar:5.8.28:compile
[INFO] |  |     |     \- cn.hutool:hutool-http:jar:5.8.28:compile
[INFO] |  |     +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] |  |     +- com.sun.xml.bind:jaxb-impl:jar:4.0.5:compile
[INFO] |  |     +- com.sun.xml.bind:jaxb-core:jar:4.0.5:compile
[INFO] |  |     |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  |     |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  |     |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |     +- javax.activation:activation:jar:1.1.1:compile
[INFO] |  |     \- cn.hutool:hutool-crypto:jar:5.8.28:compile
[INFO] |  |        \- cn.hutool:hutool-core:jar:5.8.28:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |     |  \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |     |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |     |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |     |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |     \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------< org.dromara:ruoyi-common-websocket >-----------------
[INFO] Building ruoyi-common-websocket 5.3.0                            [37/46]
[INFO]   from ruoyi-common/ruoyi-common-websocket/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common-websocket ---
[INFO] org.dromara:ruoyi-common-websocket:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |     |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  |     +- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  |     \- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |        \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  |           \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  +- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |     +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |        +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |        |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |        \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] +- org.springframework.boot:spring-boot-starter-websocket:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-messaging:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-websocket:jar:6.2.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------------< org.dromara:ruoyi-common >----------------------
[INFO] Building ruoyi-common 5.3.0                                      [38/46]
[INFO]   from ruoyi-common/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-common ---
[INFO] org.dromara:ruoyi-common:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ------------------< org.dromara:ruoyi-monitor-admin >-------------------
[INFO] Building ruoyi-monitor-admin 5.3.0                               [39/46]
[INFO]   from ruoyi-extend/ruoyi-monitor-admin/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-monitor-admin ---
[INFO] org.dromara:ruoyi-monitor-admin:jar:5.3.0
[INFO] +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  \- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] +- org.springframework.boot:spring-boot-starter-security:jar:3.4.2:compile
[INFO] |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  +- org.springframework.security:spring-security-config:jar:6.4.2:compile
[INFO] |  |  \- org.springframework.security:spring-security-core:jar:6.4.2:compile
[INFO] |  |     \- org.springframework.security:spring-security-crypto:jar:6.4.2:compile
[INFO] |  \- org.springframework.security:spring-security-web:jar:6.4.2:compile
[INFO] +- de.codecentric:spring-boot-admin-starter-server:jar:3.4.1:compile
[INFO] |  +- de.codecentric:spring-boot-admin-server:jar:3.4.1:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-webflux:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-reactor-netty:jar:3.4.2:compile
[INFO] |  |  |  |  \- io.projectreactor.netty:reactor-netty-http:jar:1.2.2:compile
[INFO] |  |  |  |     +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  \- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  |     +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  |     +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  \- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  |     +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:compile
[INFO] |  |  |  |     |  \- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  |     +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:compile
[INFO] |  |  |  |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  |     |  \- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  |     \- io.projectreactor.netty:reactor-netty-core:jar:1.2.2:compile
[INFO] |  |  |  |        \- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  |           \- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  \- org.springframework:spring-webflux:jar:6.2.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-thymeleaf:jar:3.4.2:compile
[INFO] |  |  |  \- org.thymeleaf:thymeleaf-spring6:jar:3.1.3.RELEASE:compile
[INFO] |  |  |     \- org.thymeleaf:thymeleaf:jar:3.1.3.RELEASE:compile
[INFO] |  |  |        +- org.attoparser:attoparser:jar:2.0.7.RELEASE:compile
[INFO] |  |  |        \- org.unbescape:unbescape:jar:1.1.6.RELEASE:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |  |  |  |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |  |  |        +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |  |  |        \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  |  +- org.apache.httpcomponents.client5:httpclient5:jar:5.4.1:compile
[INFO] |  |  |  +- org.apache.httpcomponents.core5:httpcore5:jar:5.3.2:compile
[INFO] |  |  |  +- org.apache.httpcomponents.core5:httpcore5-h2:jar:5.3.2:compile
[INFO] |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  \- io.projectreactor.addons:reactor-extra:jar:3.5.2:compile
[INFO] |  |     \- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- de.codecentric:spring-boot-admin-server-ui:jar:3.4.1:compile
[INFO] |  \- de.codecentric:spring-boot-admin-server-cloud:jar:3.4.1:compile
[INFO] +- de.codecentric:spring-boot-admin-starter-client:jar:3.4.1:compile
[INFO] |  \- de.codecentric:spring-boot-admin-client:jar:3.4.1:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------< org.dromara:ruoyi-snailjob-server >------------------
[INFO] Building ruoyi-snailjob-server 5.3.0                             [40/46]
[INFO]   from ruoyi-extend/ruoyi-snailjob-server/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-snailjob-server ---
[INFO] org.dromara:ruoyi-snailjob-server:jar:5.3.0
[INFO] +- com.aizuda:snail-job-server-starter:jar:1.3.0:compile
[INFO] |  +- com.aizuda:snail-job-server-web:jar:1.3.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- com.aizuda:snail-job-datasource-template:jar:1.3.0:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  |  |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  |  |  |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  |  |  |  |  +- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  |  |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  |  |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  |  |  |     \- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |     \- org.springframework:spring-**********************
[INFO] |  |  |        \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  |  +- com.aizuda:snail-job-mysql-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- com.mysql:mysql-connector-j:jar:9.1.0:compile
[INFO] |  |  +- com.aizuda:snail-job-postgres-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- org.postgresql:postgresql:jar:42.7.5:compile
[INFO] |  |  +- com.aizuda:snail-job-mariadb-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- org.mariadb.jdbc:mariadb-java-client:jar:3.4.1:compile
[INFO] |  |  |     \- com.github.waffle:waffle-jna:jar:3.3.0:compile
[INFO] |  |  |        +- net.java.dev.jna:jna:jar:5.13.0:compile
[INFO] |  |  |        +- net.java.dev.jna:jna-platform:jar:5.13.0:compile
[INFO] |  |  |        +- org.slf4j:jcl-over-slf4j:jar:2.0.16:compile
[INFO] |  |  |        \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  +- com.aizuda:snail-job-oracle-datasource:jar:1.3.0:compile
[INFO] |  |  |  +- com.oracle.database.************************************
[INFO] |  |  |  \- com.oracle.database.nls:orai18n:jar:23.5.0.24.07:compile
[INFO] |  |  +- com.aizuda:snail-job-sqlserver-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- com.microsoft.sqlserver:mssql-*****************************
[INFO] |  |  +- com.aizuda:snail-job-dm8-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- com.dameng:DmJdbcDriver18:jar:8.1.3.62:compile
[INFO] |  |  +- com.aizuda:snail-job-kingbase-datasource:jar:1.3.0:compile
[INFO] |  |  |  \- cn.com.kingbase:kingbase8:jar:8.6.0:compile
[INFO] |  |  +- org.mapstruct:mapstruct:jar:1.5.3.Final:compile
[INFO] |  |  +- org.mapstruct:mapstruct-processor:jar:1.5.3.Final:compile
[INFO] |  |  +- com.auth0:java-jwt:jar:4.4.0:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  |     +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  |  |     \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  |  \- cn.hutool:hutool-crypto:jar:5.8.32:compile
[INFO] |  |     \- cn.hutool:hutool-core:jar:5.8.32:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.3:compile
[INFO] |  +- com.aizuda:snail-job-server-retry-task:jar:1.3.0:compile
[INFO] |  |  +- com.aizuda:snail-job-common-server-api:jar:1.3.0:compile
[INFO] |  |  \- com.aizuda:snail-job-common-client-api:jar:1.3.0:compile
[INFO] |  +- com.aizuda:snail-job-server-job-task:jar:1.3.0:compile
[INFO] |  |  +- com.googlecode.aviator:aviator:jar:5.3.3:compile
[INFO] |  |  +- com.alibaba:QLExpress:jar:3.3.4:compile
[INFO] |  |  |  \- commons-lang:commons-lang:jar:2.4:compile
[INFO] |  |  \- com.aizuda:snail-job-common-log:jar:1.3.0:compile
[INFO] |  +- com.aizuda:snail-job-server-common:jar:1.3.0:compile
[INFO] |  |  +- com.typesafe.akka:akka-actor-typed_2.13:jar:2.6.21:compile
[INFO] |  |  |  +- com.typesafe.akka:akka-actor_2.13:jar:2.6.21:compile
[INFO] |  |  |  |  +- com.typesafe:config:jar:1.4.2:compile
[INFO] |  |  |  |  \- org.scala-lang.modules:scala-java8-compat_2.13:jar:1.0.0:compile
[INFO] |  |  |  +- com.typesafe.akka:akka-slf4j_2.13:jar:2.6.21:compile
[INFO] |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  +- com.google.guava:guava:jar:32.0.1-jre:compile
[INFO] |  |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:3.33.0:compile
[INFO] |  |  |  +- com.google.errorprone:error_prone_annotations:jar:2.18.0:compile
[INFO] |  |  |  \- com.google.j2objc:j2objc-annotations:jar:2.8:compile
[INFO] |  |  +- com.aizuda:snail-job-common-core:jar:1.3.0:compile
[INFO] |  |  |  +- cn.hutool:hutool-http:jar:5.8.32:compile
[INFO] |  |  |  +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |  +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  |  |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  |  |  +- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |  |  |  |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |  |  +- com.google.protobuf:protobuf-java:jar:3.25.3:compile
[INFO] |  |  |  \- com.google.api.grpc:proto-google-common-protos:jar:2.41.0:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- org.perf4j:perf4j:jar:0.9.16:compile
[INFO] |  |  +- com.squareup.okhttp3:okhttp:jar:4.12.0:compile
[INFO] |  |  |  +- com.squareup.okio:okio:jar:3.6.0:compile
[INFO] |  |  |  |  \- com.squareup.okio:okio-jvm:jar:3.6.0:compile
[INFO] |  |  |  |     \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.9.25:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.9.25:compile
[INFO] |  |  |     +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |  |  |     |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  |  |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.9.25:compile
[INFO] |  |  +- io.grpc:grpc-netty-shaded:jar:1.66.0:compile
[INFO] |  |  |  +- io.grpc:grpc-core:jar:1.66.0:compile
[INFO] |  |  |  |  +- com.google.code.gson:gson:jar:2.11.0:runtime
[INFO] |  |  |  |  +- com.google.android:annotations:jar:*******:runtime
[INFO] |  |  |  |  \- io.grpc:grpc-context:jar:1.66.0:runtime
[INFO] |  |  |  \- io.perfmark:perfmark-api:jar:0.27.0:runtime
[INFO] |  |  +- io.grpc:grpc-protobuf:jar:1.66.0:compile
[INFO] |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.66.0:runtime
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.66.0:compile
[INFO] |  |  +- io.grpc:grpc-api:jar:1.66.0:compile
[INFO] |  |  \- io.grpc:grpc-util:jar:1.66.0:compile
[INFO] |  |     \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.24:runtime
[INFO] |  +- com.aizuda:snail-job-server-ui:jar:1.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:3.4.2:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:10.1.34:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |     +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |     +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  \- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |     \- com.google.code.findbugs:jsr305:jar:2.0.2:compile
[INFO] +- org.scala-lang:scala-library:jar:2.13.9:compile
[INFO] +- de.codecentric:spring-boot-admin-starter-client:jar:3.4.1:compile
[INFO] |  \- de.codecentric:spring-boot-admin-client:jar:3.4.1:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |        +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |        |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |        \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |           \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |              +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |              \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] -----------------------< org.dromara:ruoyi-job >------------------------
[INFO] Building ruoyi-job 5.3.0                                         [41/46]
[INFO]   from ruoyi-modules/ruoyi-job/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-job ---
[INFO] org.dromara:ruoyi-job:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |     +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |        \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |     +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |     |  \- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |     +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |     |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |     |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |     |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |     |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |     |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |     |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |     |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |     |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |     |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |     +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |     +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |     +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |     +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |     |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |     |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |     +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |     |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |     |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |     |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |     +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |     |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |     \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.dromara:ruoyi-common-job:jar:5.3.0:compile
[INFO] |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  +- com.aizuda:snail-job-client-starter:jar:1.3.0:compile
[INFO] |  |  \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] |  \- com.aizuda:snail-job-client-job-core:jar:1.3.0:compile
[INFO] |     +- com.aizuda:snail-job-common-server-api:jar:1.3.0:compile
[INFO] |     |  \- com.aizuda:snail-job-common-core:jar:1.3.0:compile
[INFO] |     |     +- cn.hutool:hutool-http:jar:5.8.32:compile
[INFO] |     |     +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |     |     |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |     |     +- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |     |     |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |     |     +- com.google.protobuf:protobuf-java:jar:3.25.3:compile
[INFO] |     |     \- com.google.api.grpc:proto-google-common-protos:jar:2.41.0:compile
[INFO] |     +- com.aizuda:snail-job-common-client-api:jar:1.3.0:compile
[INFO] |     +- com.aizuda:snail-job-client-common:jar:1.3.0:compile
[INFO] |     |  +- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |     |  +- com.aizuda:snail-job-common-log:jar:1.3.0:compile
[INFO] |     |  |  \- cn.hutool:hutool-core:jar:5.8.32:compile
[INFO] |     |  +- io.grpc:grpc-netty-shaded:jar:1.66.0:compile
[INFO] |     |  |  +- io.grpc:grpc-core:jar:1.66.0:compile
[INFO] |     |  |  |  +- com.google.code.gson:gson:jar:2.11.0:runtime
[INFO] |     |  |  |  +- com.google.android:annotations:jar:*******:runtime
[INFO] |     |  |  |  \- io.grpc:grpc-context:jar:1.66.0:runtime
[INFO] |     |  |  \- io.perfmark:perfmark-api:jar:0.27.0:runtime
[INFO] |     |  +- io.grpc:grpc-protobuf:jar:1.66.0:compile
[INFO] |     |  |  \- io.grpc:grpc-protobuf-lite:jar:1.66.0:runtime
[INFO] |     |  +- io.grpc:grpc-stub:jar:1.66.0:compile
[INFO] |     |  +- io.grpc:grpc-api:jar:1.66.0:compile
[INFO] |     |  \- io.grpc:grpc-util:jar:1.66.0:compile
[INFO] |     |     \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.24:runtime
[INFO] |     +- com.google.guava:guava:jar:32.0.1-jre:compile
[INFO] |     |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |     |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |     |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |     |  +- org.checkerframework:checker-qual:jar:3.33.0:compile
[INFO] |     |  +- com.google.errorprone:error_prone_annotations:jar:2.18.0:compile
[INFO] |     |  \- com.google.j2objc:j2objc-annotations:jar:2.8:compile
[INFO] |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |     \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |        +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |        \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ---------------------< org.dromara:ruoyi-workflow >---------------------
[INFO] Building ruoyi-workflow 5.3.0                                    [42/46]
[INFO]   from ruoyi-modules/ruoyi-workflow/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-workflow ---
[INFO] org.dromara:ruoyi-workflow:jar:5.3.0
[INFO] +- org.dromara:ruoyi-common-sse:jar:5.3.0:compile
[INFO] |  +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |     +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |     \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] |  +- org.dromara:ruoyi-common-redis:jar:5.3.0:compile
[INFO] |  |  +- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:3.37.0:compile
[INFO] |  |  |  \- com.google.errorprone:error_prone_annotations:jar:2.21.1:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.2:compile
[INFO] |  |  +- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.2:compile
[INFO] |  |  |  +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.projectreactor:reactor-core:jar:3.7.2:compile
[INFO] |  |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  |  |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  |  |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.2:compile
[INFO] |  |  |  |  +- net.bytebuddy:byte-buddy:jar:1.15.11:compile
[INFO] |  |  |  |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  \- com.baomidou:lock4j-redisson-spring-boot-starter:jar:2.2.7:compile
[INFO] |  |     \- com.baomidou:lock4j-core:jar:2.2.7:compile
[INFO] |  +- org.dromara:ruoyi-common-satoken:jar:5.3.0:compile
[INFO] |  |  \- cn.dev33:sa-token-jwt:jar:1.40.0:compile
[INFO] |  |     \- cn.hutool:hutool-jwt:jar:5.8.20:compile
[INFO] |  |        +- cn.hutool:hutool-json:jar:5.8.20:compile
[INFO] |  |        |  \- cn.hutool:hutool-core:jar:5.8.20:compile
[INFO] |  |        \- cn.hutool:hutool-crypto:jar:5.8.20:compile
[INFO] |  \- org.dromara:ruoyi-common-json:jar:5.3.0:compile
[INFO] |     \- com.fasterxml.jackson.core:jackson-core:jar:2.18.2:compile
[INFO] +- org.dromara:ruoyi-common-doc:jar:5.3.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springdoc:springdoc-openapi-starter-webmvc-api:jar:2.8.4:compile
[INFO] |  |  \- org.springdoc:springdoc-openapi-starter-common:jar:2.8.4:compile
[INFO] |  |     \- io.swagger.core.v3:swagger-core-jakarta:jar:2.2.28:compile
[INFO] |  |        +- io.swagger.core.v3:swagger-annotations-jakarta:jar:2.2.28:compile
[INFO] |  |        \- io.swagger.core.v3:swagger-models-jakarta:jar:2.2.28:compile
[INFO] |  +- com.github.therapi:therapi-runtime-javadoc:jar:0.15.0:compile
[INFO] |  \- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.18.2:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-reflect:jar:1.9.25:compile
[INFO] |        \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |           \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- org.dromara:ruoyi-common-mail:jar:5.3.0:compile
[INFO] |  +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  \- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |     \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] +- org.dromara:ruoyi-common-sms:jar:5.3.0:compile
[INFO] |  \- org.dromara.sms4j:sms4j-spring-boot-starter:jar:3.3.3:compile
[INFO] |     +- org.dromara.sms4j:sms4j-core:jar:3.3.3:compile
[INFO] |     |  \- org.dromara.sms4j:sms4j-provider:jar:3.3.3:compile
[INFO] |     |     +- org.dromara.sms4j:sms4j-api:jar:3.3.3:compile
[INFO] |     |     |  \- org.dromara.sms4j:sms4j-comm:jar:3.3.3:compile
[INFO] |     |     |     \- cn.hutool:hutool-http:jar:5.8.28:compile
[INFO] |     |     +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] |     |     +- com.sun.xml.bind:jaxb-impl:jar:4.0.5:compile
[INFO] |     |     +- com.sun.xml.bind:jaxb-core:jar:4.0.5:compile
[INFO] |     |     \- javax.activation:activation:jar:1.1.1:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |        +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |        |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |        |  |  \- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |        |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |        |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |        +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |        \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] +- org.dromara:ruoyi-common-mybatis:jar:5.3.0:compile
[INFO] |  +- com.baomidou:dynamic-datasource-spring-boot3-starter:jar:4.3.1:compile
[INFO] |  |  \- com.baomidou:dynamic-datasource-spring-boot-common:jar:4.3.1:compile
[INFO] |  |     \- com.baomidou:dynamic-datasource-spring:jar:4.3.1:compile
[INFO] |  |        \- com.baomidou:dynamic-datasource-creator:jar:4.3.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus:jar:3.5.10:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus-spring:jar:3.5.10:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.10:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-**********************
[INFO] |  |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |     \- org.springframework:spring-**********************
[INFO] |  +- com.baomidou:mybatis-plus-annotation:jar:3.5.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.2:compile
[INFO] |  +- com.baomidou:mybatis-plus-core:jar:3.5.10:compile
[INFO] |  |  \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  +- com.baomidou:mybatis-plus-extension:jar:3.5.10:compile
[INFO] |  +- com.github.jsqlparser:jsqlparser:jar:5.1:compile
[INFO] |  +- com.baomidou:mybatis-plus-jsqlparser:jar:3.5.10:compile
[INFO] |  |  \- com.baomidou:mybatis-plus-jsqlparser-common:jar:3.5.10:compile
[INFO] |  \- p6spy:p6spy:jar:3.9.1:compile
[INFO] +- org.dromara:ruoyi-common-web:jar:5.3.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-json:jar:3.4.2:compile
[INFO] |  |     +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.2:compile
[INFO] |  |     \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.4.2:compile
[INFO] |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.2:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.2:compile
[INFO] |     +- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |     |  \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.3:compile
[INFO] |        \- io.micrometer:micrometer-core:jar:1.14.3:compile
[INFO] |           +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.dromara:ruoyi-common-log:jar:5.3.0:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:10.1.34:compile
[INFO] |  |  \- org.apache.tomcat:tomcat-annotations-api:jar:10.1.34:compile
[INFO] |  \- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] +- org.dromara:ruoyi-common-idempotent:jar:5.3.0:compile
[INFO] |  +- org.springframework.data:spring-data-redis:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.data:spring-data-keyvalue:jar:3.4.2:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-commons:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-oxm:jar:6.2.2:compile
[INFO] |  |  |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  +- cn.dev33:sa-token-core:jar:1.40.0:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] +- org.dromara:ruoyi-common-excel:jar:5.3.0:compile
[INFO] |  \- com.alibaba:easyexcel:jar:4.0.3:compile
[INFO] |     \- com.alibaba:easyexcel-core:jar:4.0.3:compile
[INFO] |        +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |        +- org.apache.poi:poi:jar:5.2.5:compile
[INFO] |        |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |        |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |        |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |        |  +- com.zaxxer:SparseBitSet:jar:1.3:compile
[INFO] |        |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |        +- org.apache.poi:poi-ooxml:jar:5.2.5:compile
[INFO] |        |  +- org.apache.poi:poi-ooxml-lite:jar:5.2.5:compile
[INFO] |        |  +- org.apache.xmlbeans:xmlbeans:jar:5.2.0:compile
[INFO] |        |  +- org.apache.commons:commons-compress:jar:1.25.0:compile
[INFO] |        |  \- com.github.virtuald:curvesapi:jar:1.08:compile
[INFO] |        +- org.apache.commons:commons-csv:jar:1.11.0:compile
[INFO] |        \- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |           \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |              \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |                 +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |                 \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] +- org.dromara:ruoyi-common-translation:jar:5.3.0:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.2:compile
[INFO] +- org.dromara:ruoyi-common-tenant:jar:5.3.0:compile
[INFO] +- org.dromara:ruoyi-common-security:jar:5.3.0:compile
[INFO] |  \- cn.dev33:sa-token-spring-boot3-starter:jar:1.40.0:compile
[INFO] |     +- cn.dev33:sa-token-jakarta-servlet:jar:1.40.0:compile
[INFO] |     \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.40.0:compile
[INFO] +- org.dromara.warm:warm-flow-mybatis-plus-sb3-starter:jar:1.6.6:compile
[INFO] |  \- org.dromara.warm:warm-flow-mybatis-plus-sb-starter:jar:1.6.6:compile
[INFO] |     \- org.dromara.warm:warm-flow-mybatis-plus-core:jar:1.6.6:compile
[INFO] +- org.dromara.warm:warm-flow-plugin-ui-sb-web:jar:1.6.6:compile
[INFO] |  +- org.dromara.warm:warm-flow-plugin-modes-sb:jar:1.6.6:compile
[INFO] |  |  +- org.dromara.warm:warm-flow-core:jar:1.6.6:compile
[INFO] |  |  |  \- org.dom4j:dom4j:jar:2.1.3:compile
[INFO] |  |  +- org.dromara.warm:warm-flow-plugin-json:jar:1.6.6:compile
[INFO] |  |  \- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  +- org.dromara.warm:warm-flow-plugin-ui-core:jar:1.6.6:compile
[INFO] |  |  \- org.dromara.warm:warm-flow-plugin-vue3-ui:jar:1.6.6:compile
[INFO] |  +- org.springframework:spring-webmvc:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  \- org.springframework:spring-tx:jar:6.2.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ---------------------< org.dromara:ruoyi-modules >----------------------
[INFO] Building ruoyi-modules 5.3.0                                     [43/46]
[INFO]   from ruoyi-modules/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-modules ---
[INFO] org.dromara:ruoyi-modules:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ---------------------< org.dromara:ruoyi-protocol >---------------------
[INFO] Building ruoyi-protocol 5.3.0                                    [44/46]
[INFO]   from ruoyi-protocol/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-protocol ---
[INFO] org.dromara:ruoyi-protocol:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ----------------------< org.dromara:ruoyi-server >----------------------
[INFO] Building ruoyi-server 5.3.0                                      [45/46]
[INFO]   from ruoyi-server/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-server ---
[INFO] org.dromara:ruoyi-server:pom:5.3.0
[INFO] +- org.dromara:ruoyi-common-core:jar:5.3.0:compile
[INFO] |  +- org.springframework:spring-context-support:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:6.2.2:compile
[INFO] |  |  +- org.springframework:spring-context:jar:6.2.2:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.2.2:compile
[INFO] |  |  \- org.springframework:spring-core:jar:6.2.2:compile
[INFO] |  |     \- org.springframework:spring-jcl:jar:6.2.2:compile
[INFO] |  +- org.springframework:spring-web:jar:6.2.2:compile
[INFO] |  |  \- io.micrometer:micrometer-observation:jar:1.14.3:compile
[INFO] |  |     \- io.micrometer:micrometer-commons:jar:1.14.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:3.4.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.4.2:compile
[INFO] |  |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.16:compile
[INFO] |  |  |  |  |  +- ch.qos.logback:logback-core:jar:1.5.16:compile
[INFO] |  |  |  |  |  \- org.slf4j:slf4j-api:jar:2.0.16:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.16:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.34:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.4.2:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:6.2.2:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.17.0:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.12.0:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.4.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-properties-migrator:jar:3.4.2:runtime
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-configuration-metadata:jar:3.4.2:runtime
[INFO] |  |     \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:runtime
[INFO] |  +- io.github.linpeilie:mapstruct-plus-spring-boot-starter:jar:1.4.6:compile
[INFO] |  |  +- io.github.linpeilie:mapstruct-plus:jar:1.4.6:compile
[INFO] |  |  |  +- org.mapstruct:mapstruct:jar:1.5.5.Final:compile
[INFO] |  |  |  \- io.github.linpeilie:mapstruct-plus-object-convert:jar:1.4.6:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.2:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.7.0:compile
[INFO] |  +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |  +- io.netty:netty-buffer:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-haproxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-http2:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-memcache:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-mqtt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-redis:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-smtp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-socks:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-stomp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-codec-xml:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-proxy:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-rxtx:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-sctp:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-udt:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-epoll:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.117.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.117.Final:runtime
[INFO] |  |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.117.Final:runtime
[INFO] |  \- commons-codec:commons-codec:jar:1.9:compile
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] 
[INFO] ---------------------< org.dromara:ruoyi-gateway >----------------------
[INFO] Building ruoyi-gateway 5.3.0                                     [46/46]
[INFO]   from ruoyi-gateway/pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ ruoyi-gateway ---
[INFO] org.dromara:ruoyi-gateway:pom:5.3.0
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.8.1:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.8.1:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] \- cn.hutool:hutool-all:jar:5.8.35:compile
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for biz-smart-house-platform 5.3.0:
[INFO] 
[INFO] biz-smart-house-platform ........................... SUCCESS [  0.308 s]
[INFO] ruoyi-common-core .................................. SUCCESS [  0.138 s]
[INFO] ruoyi-common-json .................................. SUCCESS [  0.013 s]
[INFO] ruoyi-common-redis ................................. SUCCESS [  0.048 s]
[INFO] ruoyi-common-web ................................... SUCCESS [  0.033 s]
[INFO] ruoyi-common-satoken ............................... SUCCESS [  0.020 s]
[INFO] ruoyi-common-log ................................... SUCCESS [  0.009 s]
[INFO] ruoyi-common-mybatis ............................... SUCCESS [  0.072 s]
[INFO] ruoyi-common-tenant ................................ SUCCESS [  0.010 s]
[INFO] ruoyi-common-encrypt ............................... SUCCESS [  0.006 s]
[INFO] ruoyi-common-social ................................ SUCCESS [  0.009 s]
[INFO] ruoyi-common-sse ................................... SUCCESS [  0.007 s]
[INFO] ruoyi-common-oss ................................... SUCCESS [  0.036 s]
[INFO] ruoyi-common-translation ........................... SUCCESS [  0.004 s]
[INFO] ruoyi-common-excel ................................. SUCCESS [  0.024 s]
[INFO] ruoyi-common-idempotent ............................ SUCCESS [  0.005 s]
[INFO] ruoyi-common-sensitive ............................. SUCCESS [  0.003 s]
[INFO] ruoyi-system ....................................... SUCCESS [  0.015 s]
[INFO] ruoyi-pdkj ......................................... SUCCESS [  0.009 s]
[INFO] ruoyi-common-mail .................................. SUCCESS [  0.007 s]
[INFO] ruoyi-common-ratelimiter ........................... SUCCESS [  0.004 s]
[INFO] ruoyi-extend ....................................... SUCCESS [  0.002 s]
[INFO] ruoyi-mqtt-client .................................. SUCCESS [  0.006 s]
[INFO] ruoyi-gateway-mq ................................... SUCCESS [  0.005 s]
[INFO] ruoyi-gateway-boot ................................. SUCCESS [  0.005 s]
[INFO] ruoyi-server-base-server ........................... SUCCESS [  0.003 s]
[INFO] ruoyi-server-core .................................. SUCCESS [  0.004 s]
[INFO] ruoyi-protocol-collect ............................. SUCCESS [  0.011 s]
[INFO] ruoyi-server-mqtt-broker ........................... SUCCESS [  0.011 s]
[INFO] ruoyi-server-boot-strap ............................ SUCCESS [  0.010 s]
[INFO] ruoyi-common-doc ................................... SUCCESS [  0.015 s]
[INFO] ruoyi-generator .................................... SUCCESS [  0.020 s]
[INFO] biz-smart-house-platform-api ....................... SUCCESS [  0.063 s]
[INFO] ruoyi-common-job ................................... SUCCESS [  0.034 s]
[INFO] ruoyi-common-security .............................. SUCCESS [  0.009 s]
[INFO] ruoyi-common-sms ................................... SUCCESS [  0.009 s]
[INFO] ruoyi-common-websocket ............................. SUCCESS [  0.010 s]
[INFO] ruoyi-common ....................................... SUCCESS [  0.002 s]
[INFO] ruoyi-monitor-admin ................................ SUCCESS [  0.020 s]
[INFO] ruoyi-snailjob-server .............................. SUCCESS [  0.045 s]
[INFO] ruoyi-job .......................................... SUCCESS [  0.005 s]
[INFO] ruoyi-workflow ..................................... SUCCESS [  0.041 s]
[INFO] ruoyi-modules ...................................... SUCCESS [  0.001 s]
[INFO] ruoyi-protocol ..................................... SUCCESS [  0.001 s]
[INFO] ruoyi-server ....................................... SUCCESS [  0.003 s]
[INFO] ruoyi-gateway ...................................... SUCCESS [  0.001 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  1.405 s
[INFO] Finished at: 2025-05-29T16:59:48+08:00
[INFO] ------------------------------------------------------------------------
