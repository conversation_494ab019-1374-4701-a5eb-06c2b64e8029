package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.pdkj.domain.Node;

import java.io.Serial;
import java.io.Serializable;



/**
 * 楼栋管理视图对象 tb_node
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Node.class)
public class NodeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 上级ID
     */
    @ExcelProperty(value = "上级ID")
    private Long parentId;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String nodeName;

    /**
     * 层级
     */
    @ExcelProperty(value = "层级")
    private Integer nodeLevel;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 节点路径
     */
    @ExcelProperty(value = "节点路径")
    private String path;

    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区ID")
    private Long estateId;


}
