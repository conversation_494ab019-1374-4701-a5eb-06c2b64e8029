package org.dromara.pdkj.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.vo.DeviceVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface DeviceMapper extends BaseMapperPlus<Device, DeviceVo> {

    default Device selectDeviceByDeviceCode(String deviceCode) {
        return BaseMapperPlus.super.selectOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceCode, deviceCode).last("limit 1"));
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default List<DeviceVo> selectVoList(Wrapper<Device> wrapper) {
        return BaseMapperPlus.super.selectVoList(wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default <P extends IPage<DeviceVo>> P selectVoPage(IPage<Device> page, Wrapper<Device> wrapper) {
        return BaseMapperPlus.super.selectVoPage(page, wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }

//    @Override
//    @DataPermission({
//        @DataColumn(key = "deptName", value = "create_dept")
//    })
//    int updateById(@Param(Constants.ENTITY) Device entity);

}
