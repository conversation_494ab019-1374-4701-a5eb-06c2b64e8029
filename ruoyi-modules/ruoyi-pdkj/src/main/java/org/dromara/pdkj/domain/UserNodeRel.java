package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 住户管理对象 tb_node_user_rel
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("\"user\".\"user_node_rel\"")
public class UserNodeRel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 楼栋ID
     */
    private Long nodeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * APP授权状态
     */
    private Long appAuthStatus;

    /**
     * APP授权开始时间
     */
    private Date appAuthStart;

    /**
     * APP授权结束时间
     */
    private Date appAuthEnd;

    /**
     * 人脸授权状态
     */
    private Long faceAuthStatus;

    /**
     * 人脸授权开始
     */
    private Date faceAuthStart;

    /**
     * 人脸授权结束
     */
    private Date faceAuthEnd;

    /**
     * 楼栋路径
     */
    private String nodePath;

    /**
     * 小区ID
     */
    private Long estateId;


}
