package org.dromara.mqtt.adapter.tcp;

import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.pdkj.service.IDeviceInventoryService;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020/4/14 15:31:12
 */
@Component
@Slf4j
@ChannelHandler.Sharable
public class LancenTcpRegisterMessageAdapter extends SimpleChannelInboundHandler<String> {

    //后台服务ID对应的channel对象
    public static ConcurrentHashMap<String, Channel> adminChannelMap = new ConcurrentHashMap<>();
    //设备ID对应的channel对象
    public static ConcurrentHashMap<String, Channel> guidChannelMap = new ConcurrentHashMap<>();
    private static LancenTcpRegisterMessageAdapter adapter;

    @Resource
    protected IDeviceInventoryService  deviceInventoryService;


    @PostConstruct
    public void init() {
        adapter = this;
        adapter.deviceInventoryService = deviceInventoryService;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String msg) {
        log.info("收到客户端【{}】的原始数据 --> {} ", ctx.channel().id().asLongText(), msg);

        if (msg.startsWith("LSV251/")) { //设备注册获取uid
            String[] split = msg.split("/");
            String uid = adapter.deviceInventoryService.registerByMac(split[0], split[1], "智能锁");
            ctx.channel().writeAndFlush(uid);
        }

        ctx.channel().close();
    }


    @Override
    public void channelRegistered(ChannelHandlerContext ctx) {

    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info("[{}]下线了, 客户端数量[{}], 后台客户端数量[{}]", channel.id().asLongText(), guidChannelMap.size(), adminChannelMap.size());
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info(channel.id().asLongText() + "上线了");
        //channelGroup.writeAndFlush("[服务器]:" + channel.remoteAddress() + "上线了\r\n");
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        log.info(channel.id().asLongText() + "离开了");
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }


}
