package org.dromara.mqtt.handler.mqtt.impl;

import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessage;
import io.netty.handler.codec.mqtt.MqttMessageIdVariableHeader;
import io.netty.handler.codec.mqtt.MqttMessageType;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.mqtt.annotation.Process;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.service.IMessageStore;
import org.dromara.mqtt.utils.MqttMessageUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 消息等级=Qos2 发布消息完成
 *
 * <AUTHOR>
 */
@Process(type = MqttMessageType.PUBCOMP)
@Slf4j
public class MqttPubcomp implements MqttHandler {

    @Autowired
    private IMessageStore messageStore;

    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        MqttMessageIdVariableHeader variableHeader = MqttMessageUtils.getIdVariableHeader(message);
        Session session = AttributeUtils.getSession(ctx.channel());
        //获取packetId
        int messageId = variableHeader.messageId();
        messageStore.removeRelOutMsg(messageId);
        /*更新平台ping*/
        ClientManager.updatePing(session.getClientId());
    }
}
