package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 尚云p2p对象 license
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("p2p.license")
public class License extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Integer delFlag;

    /**
     * $column.columnComment
     */
    @TableId(value = "id")
    private Long id;

    /**
     * DID
     */
    private String did;

    /**
     * license
     */
    private String license;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 备注
     */
    private String remark;


}
