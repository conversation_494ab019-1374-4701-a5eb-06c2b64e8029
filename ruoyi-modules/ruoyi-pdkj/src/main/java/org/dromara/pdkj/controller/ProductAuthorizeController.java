package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.ProductAuthorizeAddBo;
import org.dromara.pdkj.domain.bo.ProductAuthorizeBo;
import org.dromara.pdkj.domain.vo.ProductAuthorizeVo;
import org.dromara.pdkj.service.IProductAuthorizeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品授权码
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/productAuthorize")
public class ProductAuthorizeController extends BaseController {

    private final IProductAuthorizeService productAuthorizeService;

    /**
     * 查询产品授权码列表
     */
    @SaCheckPermission("pdkj:productAuthorize:list")
    @GetMapping("/list")
    public TableDataInfo<ProductAuthorizeVo> list(ProductAuthorizeBo bo, PageQuery pageQuery) {
        return productAuthorizeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品授权码列表
     */
    @SaCheckPermission("pdkj:productAuthorize:export")
    @Log(title = "产品授权码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductAuthorizeBo bo, HttpServletResponse response) {
        List<ProductAuthorizeVo> list = productAuthorizeService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品授权码", ProductAuthorizeVo.class, response);
    }

    /**
     * 新增产品授权码
     */
    @SaCheckPermission("pdkj:productAuthorize:add")
    @Log(title = "产品授权码", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> addProductAuthorizeByNum(@Validated(AddGroup.class) @RequestBody ProductAuthorizeAddBo bo) {
        return toAjax(productAuthorizeService.createProductAuthorizeByNum(bo));
    }


    /**
     * 删除产品授权码
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:productAuthorize:remove")
    @Log(title = "产品授权码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(productAuthorizeService.deleteWithValidByIds(List.of(ids), true));
    }
}
