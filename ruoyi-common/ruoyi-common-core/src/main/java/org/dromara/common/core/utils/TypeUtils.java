package org.dromara.common.core.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

public class TypeUtils {

    private static final Map<String, Function<String, ?>> CONVERTERS = new HashMap<>();

    static {
        CONVERTERS.put("integer", Integer::parseInt);
        CONVERTERS.put("long", Long::parseLong);
        CONVERTERS.put("float", Float::parseFloat);
        CONVERTERS.put("double", Double::parseDouble);
        CONVERTERS.put("boolean", Boolean::parseBoolean);
        CONVERTERS.put("bool", Boolean::parseBoolean);
        CONVERTERS.put("byte", Byte::parseByte);
        CONVERTERS.put("short", Short::parseShort);
        CONVERTERS.put("object", JSONUtil::parseObj);
        CONVERTERS.put("array", JSONUtil::parseArray);
    }

    public static Object convert(String type, Object value) {
        if (value == null || String.valueOf(value).trim().isEmpty()) {
            return getDefaultValue(type); // 返回对应类型的默认值
        }

        Function<String, ?> converter = CONVERTERS.get(type);
        if (converter != null) {
            try {
                return converter.apply(String.valueOf(value));
            } catch (Exception e) {
                return getDefaultValue(type); // 转换失败时返回对应类型的默认值
            }
        }
        return value; // 不支持的类型返回原始值
    }

    private static Object getDefaultValue(String type) {
        switch (type) {
            case "integer", "short", "byte":
                return 0;
            case "long":
                return 0L;
            case "float":
                return 0.0f;
            case "double":
                return 0.0;
            case "boolean":
            case "bool":
                return false;
            case "decimal":
                return new DecimalFormat("0");
            case "object":
                return new JSONObject();
            case "array":
                return new ArrayList<>();
            default:
                return null; // 不支持的类型返回 null
        }
    }

}
