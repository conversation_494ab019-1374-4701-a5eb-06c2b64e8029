package org.dromara.pdkj.domain.vo;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.pdkj.domain.Product;

import java.io.Serial;
import java.io.Serializable;


/**
 * 产品管理视图对象 tb_product
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Product.class)
public class ProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String productName;

    /**
     * 产品分类
     */
    @ExcelProperty(value = "产品分类")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 物模型
     */
    @ExcelProperty(value = "物模型")
    private JSONObject thingsModelsJson;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_device_type")
    private Integer deviceType;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_product_status")
    private Integer status;

    /**
     * 联网方式
     */
    @ExcelProperty(value = "联网方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_network_type")
    private Long networkMethod;

    /**
     * 图片地址
     */
    @ExcelProperty(value = "图片地址")
    private String imgUrl;

    /**
     * 图片地址Url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "imgUrl")
    private String imgUrlUrl;
    /**
     * 产品支持的传输协议
     */
    @ExcelProperty(value = "产品支持的传输协议")
    private String transport;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * mqtt账号
     */
    @ExcelProperty(value = "mqtt账号")
    private String mqttAccount;

    /**
     * mqtt密码
     */
    @ExcelProperty(value = "mqtt密码")
    private String mqttPassword;

    /**
     * 产品秘钥
     */
    @ExcelProperty(value = "产品秘钥")
    private String mqttSecret;

    /**
     * 认证方式
     */
    @ExcelProperty(value = "认证方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_vertificate_method")
    private Long vertificateMethod;

    /**
     * 协议编号
     */
    @ExcelProperty(value = "协议编号")
    private String protocolCode;

    private Boolean isAuthorize;

    private String productSymbol;

}
