package org.dromara.mqtt.codec;

import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.codec.MessageEncoder;
import org.dromara.common.core.protocol.Message;

@Slf4j
public class TcpMessageEncoder implements MessageEncoder {
    @Override
    public ByteBuf encode(Message message, String clientId) {
        log.info("tcp message encoder [{}]", message);
        return null;
    }
}
