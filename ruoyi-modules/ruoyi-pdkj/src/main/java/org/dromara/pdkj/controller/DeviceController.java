package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.DeviceBo;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备管理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/device")
public class DeviceController extends BaseController {

    private final IDeviceService deviceService;

    /**
     * 查询设备管理列表
     */
    @SaCheckPermission("pdkj:device:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceVo> list(DeviceBo bo, PageQuery pageQuery) {
        return deviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备管理列表
     */
    @SaCheckPermission("pdkj:device:export")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceBo bo, HttpServletResponse response) {
        List<DeviceVo> list = deviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备管理", DeviceVo.class, response);
    }

    /**
     * 获取设备管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:device:query")
    @GetMapping("/{id}")
    public R<DeviceVo> getInfo(@NotNull(message = "主键不能为空")
                               @PathVariable Long id) {
        return R.ok(deviceService.queryById(id));
    }

    @SaCheckPermission("pdkj:device:query")
    @GetMapping("/runningStatus")
    public R<DeviceVo> runningStatus(Long id, Integer slaveId) {
        return R.ok(deviceService.selectDeviceRunningStatusByDeviceId(id, slaveId));
    }

    /**
     * 新增设备管理
     */
    @SaCheckPermission("pdkj:device:add")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceBo bo) {
        return toAjax(deviceService.insertByBo(bo));
    }

    /**
     * 修改设备管理
     */
    @SaCheckPermission("pdkj:device:edit")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceBo bo) {
        return toAjax(deviceService.updateByBo(bo));
    }

    /**
     * 删除设备管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:device:remove")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(deviceService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 生成设备编号
     */
    @SaCheckPermission("pdkj:device:edit")
    @GetMapping("/generator")
    public R<String> generator(Integer deviceType) {
        return R.ok("获取成功", deviceService.generationDeviceCode(deviceType));
    }

    /**
     * 根据productId统计设备
     */
    @SaCheckPermission("pdkj:device:list")
    @GetMapping("/countByProductId")
    public R<Long> countByProductId(@RequestParam("productId") Long productId) {
        return R.ok(deviceService.countByProductId(productId));
    }
}
