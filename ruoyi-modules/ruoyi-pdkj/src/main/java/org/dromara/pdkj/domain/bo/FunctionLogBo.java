package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.FunctionLog;

import java.util.Date;

/**
 * 下发日志业务对象 tb_function_log
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FunctionLog.class, reverseConvertGenerate = false)
public class FunctionLogBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 标识符
     */
    @NotBlank(message = "标识符不能为空", groups = { AddGroup.class, EditGroup.class })
    private String identify;

    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer funType;

    /**
     * 日志值
     */
    @NotBlank(message = "日志值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String funValue;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceCode;

    /**
     * 模式
     */
    private Integer mode;

    /**
     * 下发结果描述
     */
    private String resultMsg;

    /**
     * 下发结果代码
     */
    private Integer resultCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示值
     */
    private String showValue;

    /**
     * 物模型名称
     */
    private String modelName;

    /**
     * 设备回复时间
     */
    private Date replyTime;


}
