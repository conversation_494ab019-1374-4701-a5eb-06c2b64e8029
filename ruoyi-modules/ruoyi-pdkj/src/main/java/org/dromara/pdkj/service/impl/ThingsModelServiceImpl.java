package org.dromara.pdkj.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisKeyBuilder;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.pdkj.domain.Product;
import org.dromara.pdkj.domain.ThingsModel;
import org.dromara.pdkj.domain.ThingsModelTemplate;
import org.dromara.pdkj.domain.bo.ImportThingsModelBo;
import org.dromara.pdkj.domain.bo.ThingsModelBo;
import org.dromara.pdkj.domain.dto.PropertyDto;
import org.dromara.pdkj.domain.vo.ThingsModelVo;
import org.dromara.pdkj.mapper.ProductMapper;
import org.dromara.pdkj.mapper.ThingsModelMapper;
import org.dromara.pdkj.mapper.ThingsModelTemplateMapper;
import org.dromara.pdkj.service.IThingsModelService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物模型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@RequiredArgsConstructor
@Service
public class ThingsModelServiceImpl implements IThingsModelService {

    private final ThingsModelMapper baseMapper;
    private final ProductMapper productMapper;
    private final ThingsModelTemplateMapper thingsModelTemplateMapper;

    /**
     * 查询物模型
     *
     * @param id 主键
     * @return 物模型
     */
    @Override
    public ThingsModelVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询物模型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 物模型分页列表
     */
    @Override
    public TableDataInfo<ThingsModelVo> queryPageList(ThingsModelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ThingsModel> lqw = buildQueryWrapper(bo);
        Page<ThingsModelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的物模型列表
     *
     * @param bo 查询条件
     * @return 物模型列表
     */
    @Override
    public List<ThingsModelVo> queryList(ThingsModelBo bo) {
        LambdaQueryWrapper<ThingsModel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ThingsModel> buildQueryWrapper(ThingsModelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ThingsModel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ThingsModel::getId);
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), ThingsModel::getModelName, bo.getModelName());
        lqw.eq(bo.getProductId() != null, ThingsModel::getProductId, bo.getProductId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ThingsModel::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getIdentifier()), ThingsModel::getIdentifier, bo.getIdentifier());
        lqw.eq(bo.getType() != null, ThingsModel::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDatatype()), ThingsModel::getDatatype, bo.getDatatype());
        lqw.eq(bo.getIsChart() != null, ThingsModel::getIsChart, bo.getIsChart());
        lqw.eq(bo.getIsMonitor() != null, ThingsModel::getIsMonitor, bo.getIsMonitor());
        lqw.eq(bo.getIsHistory() != null, ThingsModel::getIsHistory, bo.getIsHistory());
        lqw.eq(bo.getIsReadonly() != null, ThingsModel::getIsReadonly, bo.getIsReadonly());
        lqw.eq(bo.getIsSharePerm() != null, ThingsModel::getIsSharePerm, bo.getIsSharePerm());
        lqw.eq(bo.getModelOrder() != null, ThingsModel::getModelOrder, bo.getModelOrder());
        lqw.eq(bo.getTempSlaveId() != null, ThingsModel::getTempSlaveId, bo.getTempSlaveId());
        lqw.eq(StringUtils.isNotBlank(bo.getFormula()), ThingsModel::getFormula, bo.getFormula());
        lqw.eq(bo.getValueType() != null, ThingsModel::getValueType, bo.getValueType());
        lqw.eq(bo.getIsParams() != null, ThingsModel::getIsParams, bo.getIsParams());
        return lqw;
    }

    /**
     * 新增物模型
     *
     * @param bo 物模型
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ThingsModelBo bo) {
        ThingsModel add = MapstructUtils.convert(bo, ThingsModel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            setCacheThingsModelByProductId(add.getProductId());
        }
        return flag;
    }

    /**
     * 修改物模型
     *
     * @param bo 物模型
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ThingsModelBo bo) {
        ThingsModel update = MapstructUtils.convert(bo, ThingsModel.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            setCacheThingsModelByProductId(update.getProductId());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ThingsModel entity) {

        List<ThingsModelVo> thingsModelVos = baseMapper.selectVoList(new LambdaQueryWrapper<ThingsModel>().select(ThingsModel::getId, ThingsModel::getIdentifier)
            .eq(ThingsModel::getProductId, entity.getProductId()));

        boolean anyMatch = thingsModelVos.stream().anyMatch(thingsModelVo -> {
            if (entity.getId() != null && entity.getId().equals(thingsModelVo.getId()) && entity.getIdentifier().equals(thingsModelVo.getIdentifier())) {
                return false;
            }
            return thingsModelVo.getIdentifier().equals(entity.getIdentifier());
        });

        if (anyMatch) {
            throw new ServiceException("产品下的标识符不能重复");
        }

    }

    /**
     * 校验并批量删除物模型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        ThingsModel thingsModel = baseMapper.selectById(ArrayUtil.toArray(ids, Long.class)[0]);
        boolean flag = baseMapper.deleteByIds(ids) > 0;
        if (flag) {
            setCacheThingsModelByProductId(thingsModel.getProductId());
        }
        return flag;
    }

    @Override
    public String getCacheThingsModelByProductId(Long productId) {
        if (productId == null) {
            throw new ServiceException("产品id为空");
        }
        Map<String, String> map = RedisUtils.getCacheMap(RedisKeyBuilder.buildTSLCacheKey(productId));
        if (!CollectionUtils.isEmpty(map)) {
            //兼容原页面物模型的数据格式
            Map<String, List<PropertyDto>> listMap = map.values().stream()
                .map(v -> JSONUtil.toBean(v, PropertyDto.class))
                .collect(Collectors.groupingBy(dto -> ThingsModelType.getName(dto.getType())));
            return JSONUtil.toJsonStr(listMap);
        }
        return setCacheThingsModelByProductId(productId);

    }

    public PropertyDto getSingleThingModels(Long productId, String identify) {
        PropertyDto dto = new PropertyDto();
        String cacheKey = RedisKeyBuilder.buildTSLCacheKey(productId);
        String value = RedisUtils.getCacheMapValue(cacheKey, identify);
        //缓存没有则先查询数据库
        if (StringUtils.isEmpty(value)) {
            ThingsModel thingsModel = new ThingsModel();
            if (identify.contains("#")) {
                String[] split = identify.split("#");
                identify = split[0];
                thingsModel.setTempSlaveId(split[1]);
            }
            thingsModel.setIdentifier(identify);
            thingsModel.setProductId(productId);
            ThingsModel selectModel = this.selectSingleThingsModel(thingsModel);
            // redis和数据库都没有则是对象或数组类型。 兼容对象类型和数组类型
            if (StringUtils.isNull(dto.getId()) && identify.contains("_")) {
                String[] split = identify.split("_");
                String thingsM = RedisUtils.getCacheMapValue(cacheKey, split[0]);
                if (StringUtils.isNull(thingsM)) {
                    return null;
                }
                PropertyDto subDto = JSONUtil.toBean(thingsM, PropertyDto.class);
                JSONArray array = JSONUtil.parseArray(String.valueOf(subDto.getDatatype().get("params")));
                String finalIdentify = identify;
                PropertyDto propertyDto = array.toList(PropertyDto.class).stream()
                    .filter(x -> x.getId().equals(finalIdentify))
                    .findFirst().get();
                propertyDto.setType(subDto.getType());
                return propertyDto;
            }
            if (null == selectModel) {
                return dto;
            }
            BeanUtils.copyProperties(selectModel, dto);
            dto.setId(selectModel.getIdentifier());
            dto.setName(selectModel.getModelName());
            dto.setIsParams(selectModel.getIsParams());
            dto.setDatatype(JSONUtil.parseObj(selectModel.getSpecs()));
            dto.setOrder(selectModel.getModelOrder());
            dto.setFormula(selectModel.getFormula());
            dto.setTempSlaveId(selectModel.getTempSlaveId());
            this.setCacheThingsModelByProductId(productId);
        } else {
            dto = JSONUtil.toBean(value, PropertyDto.class);
        }
        return dto;
    }

    @Override
    public int importByTemplateIds(ImportThingsModelBo input) {

        Product product = productMapper.selectById(input.getProductId());
        if (product == null) {
            throw new ServiceException("产品不存在");
        }
        // 物模型标识符不能重复 TODO 重复查询待优化
        ThingsModel inputParameter = new ThingsModel();
        inputParameter.setProductId(input.getProductId());
        List<ThingsModelVo> dbList = baseMapper.selectVoList(new LambdaQueryWrapper<ThingsModel>().eq(ThingsModel::getProductId, input.getProductId()));

        // 根据ID集合获取通用物模型列表
        List<ThingsModelTemplate> templateList = thingsModelTemplateMapper.selectList(new LambdaQueryWrapper<ThingsModelTemplate>()
            .in(ThingsModelTemplate::getId, (Object[]) input.getTemplateIds()));
        //转换为产品物模型，并批量插入
        List<ThingsModel> list = new ArrayList<>();
        int repeatCount = 0;
        for (ThingsModelTemplate template : templateList) {
            ThingsModel thingsModel = new ThingsModel();
            BeanUtils.copyProperties(template, thingsModel);
            thingsModel.setCreateTime(DateUtils.getNowDate());
            thingsModel.setProductId(input.getProductId());
            thingsModel.setProductName(product.getProductName());
            thingsModel.setId(template.getId());
            thingsModel.setModelName(template.getTemplateName());
            thingsModel.setIsReadonly(template.getIsReadonly());
            thingsModel.setIsMonitor(template.getIsMonitor());
            thingsModel.setIsChart(template.getIsChart());
            thingsModel.setIsSharePerm(template.getIsSharePerm());
            thingsModel.setIsHistory(template.getIsHistory());
            thingsModel.setModelOrder(template.getModelOrder());
            //兼容modbsu
            if (StringUtils.isNotEmpty(template.getTempSlaveId())) {
                thingsModel.setTempSlaveId(template.getTempSlaveId().split("#")[1]);
            }
            boolean isRepeat = dbList.stream().anyMatch(x -> x.getIdentifier().equals(thingsModel.getIdentifier()));
            if (isRepeat) {
                repeatCount = repeatCount + 1;
            } else {
                list.add(thingsModel);
            }
        }
        if (!list.isEmpty()) {
            baseMapper.insertBatch(list);
            //更新redis缓存
            setCacheThingsModelByProductId(input.getProductId());
        }
        return repeatCount;
    }

    private ThingsModel selectSingleThingsModel(ThingsModel thingsModel) {

        return baseMapper.selectOne(new LambdaQueryWrapper<ThingsModel>()
            .eq(thingsModel.getProductId() != null && thingsModel.getProductId() > 0, ThingsModel::getProductId, thingsModel.getProductId())
            .eq(StringUtils.isNotBlank(thingsModel.getIdentifier()), ThingsModel::getIdentifier, thingsModel.getIdentifier())
            .eq(StringUtils.isNotBlank(thingsModel.getTempSlaveId()), ThingsModel::getTempSlaveId, thingsModel.getTempSlaveId())
            .last("limit 1")
        );
    }


    /**
     * 根据产品ID更新JSON物模型
     *
     * @param productId
     * @return
     */
    private String setCacheThingsModelByProductId(Long productId) {
        // 数据库查询物模型集合
        ThingsModel model = new ThingsModel();
        model.setProductId(productId);
        List<ThingsModel> thingsModels = baseMapper.selectList(new LambdaQueryWrapper<ThingsModel>().eq(ThingsModel::getProductId, productId));
        /*这里key 1.非modbus为 identify  2. 是modbus设备时使用 identify#设备编号*/
        Map<String, String> things = thingsModels.stream()
            .collect(Collectors.toMap(key -> key.getIdentifier() + (key.getTempSlaveId() == null ? "" : "#" + key.getTempSlaveId()),
                value -> {
                    //转换数据，减少不必要数据
                    PropertyDto dto = new PropertyDto();
                    BeanUtils.copyProperties(value, dto);
                    dto.setDatatype(value.getSpecs());
                    dto.setId(value.getIdentifier());
                    dto.setRegId(value.getIdentifier() + (value.getTempSlaveId() == null ? "" : "#" + value.getTempSlaveId()));
                    dto.setName(value.getModelName());
                    dto.setOrder(value.getModelOrder());
                    dto.setQuantity(value.getQuantity());
                    dto.setCode(value.getCode());
                    return JSONUtil.toJsonStr(dto);
                }));

        /*缓存到redis*/
        String cacheKey = RedisKeyBuilder.buildTSLCacheKey(productId);
        //先删除缓存
        RedisUtils.deleteObject(cacheKey);
        RedisUtils.setCacheMap(cacheKey, things);
        /*组装成原格式数据*/
        Map<String, List<PropertyDto>> result = things.values().stream().map(x -> JSONUtil.toBean(x, PropertyDto.class))
            .collect(Collectors.groupingBy(dto -> ThingsModelType.getName(dto.getType())));
        String jsonString = JSONUtil.toJsonStr(result);
        Product product = new Product();
        product.setId(productId);
        product.setThingsModelsJson(JSONUtil.parseObj(result));
        productMapper.updateById(product);
        return jsonString;
    }
}
