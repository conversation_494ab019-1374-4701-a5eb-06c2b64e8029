package org.dromara.common.core.thingsModel;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * redis缓存物模型值
 *
 * <AUTHOR>
 * @date 2023/6/3 13:48
 */
@Data
@NoArgsConstructor
public class ValueItem {

    public ValueItem(String id, String slaveId, String regArr) {
        this.id = id;
        this.slaveId = slaveId;
        this.regArr = regArr;
    }

    /**
     * 标识符
     */
    private String id;

    /**
     * 物模型值
     */
    private Object value;

    /**
     * 影子值
     **/
    private Object shadow;

    /**
     * 物模型名称
     **/
    private String name;

    /**
     * 上报时间
     */
    private Date ts;

    /**
     * 从机编号
     */
    private String slaveId;

    /**
     * 寄存器地址
     */
    private String regArr;

    private String remark;

}
