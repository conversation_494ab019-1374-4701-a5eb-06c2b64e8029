package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.Node;

/**
 * 楼栋管理业务对象 tb_node
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Node.class, reverseConvertGenerate = false)
public class NodeBo extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nodeName;

    /**
     * 层级
     */
    @NotNull(message = "层级不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer nodeLevel;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 节点路径
     */
    private String path;

    /**
     * 小区ID
     */
    @NotNull(message = "小区ID不能为空", groups = {AddGroup.class, EditGroup.class, QueryGroup.class})
    private Long estateId;


}
