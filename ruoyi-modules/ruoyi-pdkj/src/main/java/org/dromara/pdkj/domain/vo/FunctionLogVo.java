package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.FunctionLog;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 下发日志视图对象 tb_function_log
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FunctionLog.class)
public class FunctionLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 标识符
     */
    @ExcelProperty(value = "标识符")
    private String identify;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_fun_type")
    private Integer funType;

    /**
     * 日志值
     */
    @ExcelProperty(value = "日志值")
    private String funValue;

    /**
     * 消息id
     */
    @ExcelProperty(value = "消息id")
    private String messageId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 模式
     */
    @ExcelProperty(value = "模式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ios_device_mode")
    private Integer mode;

    /**
     * 下发结果描述
     */
    @ExcelProperty(value = "下发结果描述")
    private String resultMsg;

    /**
     * 下发结果代码
     */
    @ExcelProperty(value = "下发结果代码")
    private Integer resultCode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 显示值
     */
    @ExcelProperty(value = "显示值")
    private String showValue;

    /**
     * 物模型名称
     */
    @ExcelProperty(value = "物模型名称")
    private String modelName;

    /**
     * 设备回复时间
     */
    @ExcelProperty(value = "设备回复时间")
    private Date replyTime;


}
