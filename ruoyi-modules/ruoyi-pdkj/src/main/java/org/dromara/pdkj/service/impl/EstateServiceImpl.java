package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.Estate;
import org.dromara.pdkj.domain.bo.EstateBo;
import org.dromara.pdkj.domain.vo.EstateVo;
import org.dromara.pdkj.mapper.EstateMapper;
import org.dromara.pdkj.service.IEstateService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 小区信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class EstateServiceImpl implements IEstateService {

    private final EstateMapper baseMapper;

    /**
     * 查询小区信息
     *
     * @param id 主键
     * @return 小区信息
     */
    @Override
    public EstateVo queryById(Integer id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询小区信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 小区信息分页列表
     */
    @Override
    public TableDataInfo<EstateVo> queryPageList(EstateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Estate> lqw = buildQueryWrapper(bo);
        Page<EstateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的小区信息列表
     *
     * @param bo 查询条件
     * @return 小区信息列表
     */
    @Override
    public List<EstateVo> queryList(EstateBo bo) {
        LambdaQueryWrapper<Estate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Estate> buildQueryWrapper(EstateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Estate> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Estate::getId);
        lqw.like(StringUtils.isNotBlank(bo.getEstateName()), Estate::getEstateName, bo.getEstateName());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), Estate::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeStruct()), Estate::getNodeStruct, bo.getNodeStruct());
        lqw.like(StringUtils.isNotBlank(bo.getContactName()), Estate::getContactName, bo.getContactName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), Estate::getContactPhone, bo.getContactPhone());
        return lqw;
    }

    /**
     * 新增小区信息
     *
     * @param bo 小区信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EstateBo bo) {
        Estate add = MapstructUtils.convert(bo, Estate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改小区信息
     *
     * @param bo 小区信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EstateBo bo) {
        Estate update = MapstructUtils.convert(bo, Estate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Estate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除小区信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
