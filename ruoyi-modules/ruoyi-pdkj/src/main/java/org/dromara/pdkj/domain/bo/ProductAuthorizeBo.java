package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.ProductAuthorize;

/**
 * 产品授权码业务对象 tb_product_authorize
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductAuthorize.class, reverseConvertGenerate = false)
public class ProductAuthorizeBo extends BaseEntity {

    /**
     * 授权码ID
     */
    @NotNull(message = "授权码ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 授权码
     */
    @NotBlank(message = "授权码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String authorizeCode;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;


}
