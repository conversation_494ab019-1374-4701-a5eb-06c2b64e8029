package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.DeviceInventoryAddBo;
import org.dromara.pdkj.domain.bo.DeviceInventoryBo;
import org.dromara.pdkj.domain.vo.DeviceInventoryVo;
import org.dromara.pdkj.service.IDeviceInventoryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备出厂
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/inventory")
public class DeviceInventoryController extends BaseController {

    private final IDeviceInventoryService deviceInventoryService;

    /**
     * 查询设备出厂列表
     */
    @SaCheckPermission("pdkj:inventory:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceInventoryVo> list(DeviceInventoryBo bo, PageQuery pageQuery) {
        return deviceInventoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备出厂列表
     */
    @SaCheckPermission("pdkj:inventory:export")
    @Log(title = "设备出厂", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceInventoryBo bo, HttpServletResponse response) {
        List<DeviceInventoryVo> list = deviceInventoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备出厂", DeviceInventoryVo.class, response);
    }

    /**
     * 获取设备出厂详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:inventory:query")
    @GetMapping("/{id}")
    public R<DeviceInventoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(deviceInventoryService.queryById(id));
    }

    /**
     * 新增设备出厂
     */
    @SaCheckPermission("pdkj:inventory:add")
    @Log(title = "设备出厂", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceInventoryAddBo bo) {
        return toAjax(deviceInventoryService.insertByBo(bo));
    }

    /**
     * 修改设备出厂
     */
    @SaCheckPermission("pdkj:inventory:edit")
    @Log(title = "设备出厂", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceInventoryBo bo) {
        return toAjax(deviceInventoryService.updateByBo(bo));
    }

    /**
     * 删除设备出厂
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:inventory:remove")
    @Log(title = "设备出厂", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(deviceInventoryService.deleteWithValidByIds(List.of(ids), true));
    }
}
