//package org.dromara.common.mybatis.config;
//
//import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @since 2019-11-28
// */
//@Component
//public class MpJsonConfig implements CommandLineRunner {
//
//    /**
//     * 可以set进去自己的
//     */
//    @Override
//    public void run(String... args) throws Exception {
//        JacksonTypeHandler.setObjectMapper(new ObjectMapper());
//    }
//}
