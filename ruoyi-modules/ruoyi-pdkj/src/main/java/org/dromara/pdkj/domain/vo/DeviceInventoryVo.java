package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.pdkj.domain.DeviceInventory;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 设备出厂视图对象 device_inventory
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceInventory.class)
public class DeviceInventoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品密钥
     */
    @ExcelProperty(value = "产品密钥")
    private String deviceSecret;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备别名
     */
    @ExcelProperty(value = "设备别名")
    private String deviceAlias;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态")
    private Integer deviceStatus;

    /**
     * 激活时间
     */
    @ExcelProperty(value = "激活时间")
    private Date lastActivateTime;

    /**
     * 注销时间
     */
    @ExcelProperty(value = "注销时间")
    private Date lastDeactivateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 打开时间
     */
    @ExcelProperty(value = "打开时间")
    private Long bindStatus;

    /**
     * 设备Mac
     */
    @ExcelProperty(value = "设备Mac")
    private String deviceMac;

}
