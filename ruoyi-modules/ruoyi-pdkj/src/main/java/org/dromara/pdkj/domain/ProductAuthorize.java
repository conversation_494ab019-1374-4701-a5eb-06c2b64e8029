package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 产品授权码对象 tb_product_authorize
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot.product_authorize")
public class ProductAuthorize extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 授权码ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 授权码
     */
    private String authorizeCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;


}
