package org.dromara.bootstrap.tcp;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.codec.Delimiter;
import org.dromara.common.core.enums.ServerType;
import org.dromara.mqtt.codec.TcpMessageDecoder;
import org.dromara.mqtt.codec.TcpMessageEncoder;
import org.dromara.mqtt.server.LancenTcpRegisterServer;
import org.dromara.mqtt.server.TcpServer;
import org.dromara.server.Server;
import org.dromara.server.config.NettyConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

@Order(10)
@Configuration
@ConfigurationProperties(value = "server.broker.tcp")
@Data
@Slf4j
public class TCPBootStrap {
    /*服务器集群节点*/
    private String brokerNode;
    /*端口*/
    private int port;
    /*心跳时间*/
    private int keepAlive;
    @Autowired
    private TcpServer tcpServer;
    @Autowired
    private LancenTcpRegisterServer lancenTcpRegisterServer;

    @ConditionalOnProperty(value = "server.broker.tcp.enabled", havingValue = "true")
    @Bean(initMethod = "start", destroyMethod = "stop")
    public Server tcpBroker() {
        log.info("正在启动TCP服务...端口:{}", port);
        return NettyConfig.custom()
            .setIdleStateTime(keepAlive, 0, 0)
            .setPort(port)
            .setMaxFrameLength(100)
            .setDelimiters(new Delimiter(new byte[]{0x7e}, true))
            .setDecoder(new TcpMessageDecoder())
            .setEncoder(new TcpMessageEncoder())
            .setServer(tcpServer)
            .setName(ServerType.TCP.getDes())
            .setType(ServerType.TCP)
            .build();
    }

    @Bean(initMethod = "start", destroyMethod = "stop")
    public Server tcpRegisterBroker() {
        log.info("正在启动TCP注册服务...端口:{}", 15300);
        return NettyConfig.custom()
            .setIdleStateTime(keepAlive, 0, 0)
            .setPort(15300)
            .setMaxFrameLength(100)
            .setDelimiters(new Delimiter(new byte[]{0x7e}, true))
            .setDecoder(new TcpMessageDecoder())
            .setEncoder(new TcpMessageEncoder())
            .setServer(lancenTcpRegisterServer)
            .setName(ServerType.TCP.getDes())
            .setType(ServerType.TCP)
            .build();
    }

}
