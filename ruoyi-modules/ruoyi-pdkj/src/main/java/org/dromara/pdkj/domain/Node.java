package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 楼栋管理对象 tb_node
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("estate.node")
public class Node extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 名称
     */
    private String nodeName;

    /**
     * 层级
     */
    private Integer nodeLevel;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Long delFlag;

    /**
     * 节点路径
     */
    private String path;

    /**
     * 小区ID
     */
    private Long estateId;


}
