package org.dromara.web.domain.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Tag(name = "配置")
public class ApiInvokeReqBo {
    @Schema(description = "产品id")
    @NotNull(message = "产品id不能为空")
    private Long productId;

    @Schema(description = "设备编码")
    @NotBlank(message = "设备编码不能为空")
    private String deviceCode;

    @Schema(description = "远程命令")
    @NotEmpty(message = "远程命令不能为空")
    private List<ThingsModelItemValue> remoteCommand;

    @Schema(description = "下发物模型类型 1=-属性，2-功能，3-事件")
    @NotNull(message = "下发物模型类型 1=-属性，2-功能，3-事件")
    private Integer type;

//    @Schema(description = "用户id")
//    @NotNull(message = "用户id不能为空")
//    private Long userId;

}
