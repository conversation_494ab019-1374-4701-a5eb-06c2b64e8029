package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.Protocol;

/**
 * 协议管理业务对象 tb_protocol
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Protocol.class, reverseConvertGenerate = false)
public class ProtocolBo extends BaseEntity {

    /**
     * 自增id
     */
    @NotNull(message = "自增id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 协议编码
     */
    @NotBlank(message = "协议编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String protocolCode;

    /**
     * 协议名称
     */
    @NotBlank(message = "协议名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String protocolName;

    /**
     * 协议jar包,js包，c程序上传地址
     */
    private String protocolFileUrl;

    /**
     * 协议类型 0:未知 1:jar，2.js,3.c
     */
    @NotNull(message = "协议类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer protocolType;

    /**
     * 协议文件摘要(文件的md5)
     */
    private String jarSign;

    /**
     * 0:草稿 1:启用 2:停用
     */
    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long protocolStatus;


}
