package org.dromara.mqtt.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.mqtt.model.Subscribe;
import org.dromara.mqtt.service.ISubscriptionService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14 8:37
 */
@Slf4j
@Component
public class SubscriptionServiceImpl implements ISubscriptionService {

    /**
     * 保存客户订阅的主题
     *
     * @param subscribeList 主题列表
     */
    @Override
    public void subscribe(List<Subscribe> subscribeList, String clientId) {
        RedisUtils.setCacheList(clientId, subscribeList);
    }

    /**
     * 解除订阅
     *
     * @param clientId  客户id
     * @param topicName 主题
     */
    @Override
    public void unsubscribe(String clientId, String topicName) {
        RedisUtils.delCacheMapValue(topicName, clientId);
    }

    /**
     * 获取订阅了 topic 的客户id
     *
     * @param topic 主题
     * @return 订阅了主题的客户id列表
     */
    @Override
    public List<Subscribe> searchSubscribeClientList(String topic) {
          return null;
    }


}
