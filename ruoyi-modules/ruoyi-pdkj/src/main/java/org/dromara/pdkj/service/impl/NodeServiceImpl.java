package org.dromara.pdkj.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.dromara.pdkj.domain.Estate;
import org.dromara.pdkj.domain.Node;
import org.dromara.pdkj.domain.UserNodeRel;
import org.dromara.pdkj.domain.bo.NodeAddBo;
import org.dromara.pdkj.domain.bo.NodeBo;
import org.dromara.pdkj.domain.vo.NodeVo;
import org.dromara.pdkj.mapper.EstateMapper;
import org.dromara.pdkj.mapper.NodeMapper;
import org.dromara.pdkj.mapper.UserNodeRelMapper;
import org.dromara.pdkj.service.INodeService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 楼栋管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class NodeServiceImpl implements INodeService {

    private final NodeMapper baseMapper;
    private final EstateMapper estateMapper;
    private final UserNodeRelMapper userNodeRelMapper;
    /**
     * 查询楼栋管理
     *
     * @param id 主键
     * @return 楼栋管理
     */
    @Override
    public NodeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询符合条件的楼栋管理列表
     *
     * @param bo 查询条件
     * @return 楼栋管理列表
     */
    @Override
    public List<NodeVo> queryList(NodeBo bo) {
        LambdaQueryWrapper<Node> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Node> buildQueryWrapper(NodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Node> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Node::getId);
        lqw.eq(bo.getParentId() != null, Node::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getNodeName()), Node::getNodeName, bo.getNodeName());
        lqw.eq(bo.getUserId() != null, Node::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getPath()), Node::getPath, bo.getPath());
        lqw.eq(bo.getEstateId() != null, Node::getEstateId, bo.getEstateId());
        return lqw;
    }

    /**
     * 新增楼栋管理
     *
     * @param bo 楼栋管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(NodeAddBo bo) {
        Estate estate = getEstate(bo.getEstateId());
        int maxNodeLength = estate.getNodeStruct().split("/").length;
        String nodePath = "-";
        int nodeLevel = 1;
        if (bo.getParentId() != null) {
            NodeVo parentNodeVo = baseMapper.selectVoById(bo.getParentId());
            if (parentNodeVo == null) {
                throw new ServiceException("上级不存在");
            }
            if (parentNodeVo.getNodeLevel() == maxNodeLength) {
                throw new ServiceException("最后一级不能添加了");
            }
            nodePath = parentNodeVo.getPath();
            nodeLevel = parentNodeVo.getNodeLevel() + 1;
        }
        Node add = MapstructUtils.convert(bo, Node.class);
        add.setNodeLevel(nodeLevel);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        //更新nodePath
        if (flag) {
            nodePath += add.getId() + "-";
            Node node = new Node();
            node.setId(add.getId());
            node.setPath(nodePath);
            baseMapper.updateById(node);
        }
        return flag;
    }

    private Estate getEstate(Long bo) {
        Estate estate = getSelectById(bo);
        if (estate == null) {
            throw new ServiceException("小区信息不存在");
        }
        return estate;
    }

    private Estate getSelectById(Long bo) {
        return estateMapper.selectById(bo);
    }

    /**
     * 修改楼栋管理
     *
     * @param bo 楼栋管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(NodeBo bo) {
        Node update = MapstructUtils.convert(bo, Node.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Node entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除楼栋管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            Long nodeCount = baseMapper.selectCount(new LambdaQueryWrapper<Node>().in(Node::getParentId, ids));
            if (nodeCount > 0) {
                throw new ServiceException("存在子节点，不能删除。");
            }
            ids.forEach(id -> {
                Node node = baseMapper.selectById(id);
                if (node == null) {
                    return;
                }
                Long userCount = userNodeRelMapper.selectCount(new LambdaQueryWrapper<UserNodeRel>().likeRight(UserNodeRel::getNodePath, node.getPath()));
                if (userCount > 0) {
                    throw new ServiceException("该节点存在用户，不能删除。");
                }
            });
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<Tree<Long>> queryTree(NodeBo bo) {
        LambdaQueryWrapper<Node> lqw = buildQueryWrapper(bo);
        List<NodeVo> nodeVos = baseMapper.selectVoList(lqw);
        Estate estate = getEstate(bo.getEstateId());
        return buildDeptTreeSelect(nodeVos, estate);
    }

    public List<Tree<Long>> buildDeptTreeSelect(List<NodeVo> nodeVoList, Estate estate) {
        // 获取当前列表中每一个节点的parentId，然后在列表中查找是否有id与其parentId对应，若无对应，则表明此时节点列表中，该节点在当前列表中属于顶级节点
        List<Tree<Long>> treeList = CollUtil.newArrayList();
        for (NodeVo d : nodeVoList) {
            Long parentId = d.getParentId();
            if (parentId == null) {
                d.setParentId(0L);
                parentId = 0L;
            }
            Long finalParentId = parentId;
            NodeVo sysDeptVo = StreamUtils.findFirst(nodeVoList, it -> it.getId().longValue() == finalParentId);
            if (ObjectUtil.isNull(sysDeptVo)) {
                List<Tree<Long>> trees = TreeBuildUtils.build(nodeVoList, parentId, (nodeVo, tree) ->
                    {
                        Tree<Long> longTree = tree.setId(nodeVo.getId())
                            .setParentId(nodeVo.getParentId())
                            .setName(nodeVo.getNodeName())
                            .setWeight(nodeVo.getNodeLevel());
                        longTree.putExtra("showButton", false);
                        longTree.putExtra("nodeLevel", nodeVo.getNodeLevel());
                        longTree.putExtra("path", nodeVo.getPath());
                        longTree.putExtra("type", "node");
                        longTree.putExtra("estateId", estate.getId());
                    }
                );
                Tree<Long> tree = StreamUtils.findFirst(trees, it -> it.getId().longValue() == d.getId());
                treeList.add(tree);
            }
        }
        List<Tree<Long>> treeRootList = CollUtil.newArrayList();
        Tree<Long> longTree = new Tree<>();
        longTree.setName(estate.getEstateName());
        longTree.setId(estate.getId());
        longTree.setWeight(0);
        longTree.putExtra("showButton", false);
        longTree.putExtra("nodeLevel", 0);
        longTree.putExtra("path", "");
        longTree.putExtra("type", "estate");
        longTree.putExtra("estateId", estate.getId());
        longTree.setChildren(treeList);
        treeRootList.add(longTree);
        return treeRootList;
    }
}
