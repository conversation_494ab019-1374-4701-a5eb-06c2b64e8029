package org.dromara.exposed.contorller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.mq.InvokeReqDto;
import org.dromara.mq.service.IFunctionInvoke;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.dromara.web.domain.bo.ApiInvokeReqBo;
import org.dromara.web.domain.bo.ApiOpenLockReqBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/v1/api/lock/device")
@RestController
@Validated
@RequiredArgsConstructor
@SaIgnore
@Tag(name = "门锁设备")
public class ApiDoorLockDeviceController {

    private final IFunctionInvoke functionInvoke;
    private final IDeviceService deviceService;

    @Operation(summary = "开锁")
    @PostMapping("/open")
    public R<String> openLock(@Parameter @Validated @RequestBody ApiOpenLockReqBo bo) {

        DeviceVo deviceVo = deviceService.selectDeviceByDeviceCode(bo.getDeviceCode());
        if (deviceVo == null) {
            return R.fail("设备不存在");
        }
        if (deviceVo.getDeviceStatus() != DeviceStatus.ONLINE.getType()) {
            return R.fail("设备" + DeviceStatus.convert(deviceVo.getDeviceStatus()).getDescription());
        }

        JSONObject entries = new JSONObject();
        entries.set("unlock", 1);

        InvokeReqDto boo = new InvokeReqDto();
        boo.setValue(entries);
        boo.setDeviceCode(bo.getDeviceCode());
        boo.setType(ThingsModelType.SERVICE.getCode());
        boo.setProductId(bo.getProductId());
        boo.setUserId(bo.getUserId());
        boo.setUserType(UserType.APP_USER);

        String messageId = functionInvoke.invokeNoReply(boo);
        return R.ok("操作成功", messageId);
    }


    @Operation(summary = "配置")
    @PostMapping("/config")
    public R<String> config(@Validated @RequestBody ApiInvokeReqBo bo) {

        JSONObject entries = new JSONObject();
        bo.getRemoteCommand().forEach(item -> entries.set(item.getId(), item.getValue()));

        InvokeReqDto boo = new InvokeReqDto();
        boo.setValue(entries);
        boo.setDeviceCode(bo.getDeviceCode());
        boo.setType(bo.getType());
        boo.setProductId(bo.getProductId());

        String messageId = functionInvoke.invokeNoReply(boo);
        return R.ok("操作成功", messageId);
    }

    @Operation(summary = "获取版本号")
    @GetMapping("/version/{productId}/{deviceCode}")
    public R<String> gerVersion(@PathVariable Long productId, @PathVariable String deviceCode) {

        JSONObject entries = new JSONObject();
        entries.set("get_version", 1);

        InvokeReqDto boo = new InvokeReqDto();
        boo.setValue(entries);
        boo.setProductId(productId);
        boo.setDeviceCode(deviceCode);
        boo.setType(ThingsModelType.PROP.getCode());

        String messageId = functionInvoke.invokeNoReply(boo);
        return R.ok("操作成功", messageId);
    }

}
