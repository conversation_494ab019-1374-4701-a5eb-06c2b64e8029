package org.dromara.common.core.mq.message;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.core.enums.ServerType;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.protocol.modbus.ModbusCode;

import java.util.List;

/**
 * 设备下发指令model
 *
 * <AUTHOR>
 * @date 2022/10/10 16:18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDownMessage {

    private String messageId;
    /**
     * 时间戳，单位毫秒
     */
    private Long timestamp;
    /**
     * 消息体
     */
    private Object body;
    /*下发的指令,服务调用的时候就是服务标识符*/
    private String identifier;
    /*产品id*/
    private Long productId;
    /**
     * 设备编码
     */
    private String deviceCode;
    /*网关设备编码*/
    String subSerialNumber;
    /**
     * true: 表示是一条发往网关子设备的指令
     * 默认是false
     */
    @Builder.Default
    Boolean subFlag = false;
    /**
     * 从机编号
     */
    private Integer slaveId;
    private ModbusCode code;
    private int count;
    private int address;
    private String protocolCode;

    private List<PropRead> values;
    private String topic;
    private String subCode;
    private ServerType serverType;


    private Long userId;
    /**
     * 用户类型 sys_user,app_user
     */
    private UserType userType;
    /**
     * 额外信息
     */
    private JSONObject extra;

    public DeviceDownMessage(List<PropRead> values, String topic, String subCode, String transport) {
        this.values = values;
        this.topic = topic;
        this.subCode = subCode;
        this.serverType = ServerType.explain(transport);
    }
}
