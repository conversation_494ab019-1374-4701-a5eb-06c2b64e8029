package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 小区信息对象 tb_estate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("estate.estate")
public class Estate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Long delFlag;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String estateName;

    /**
     * 地址
     */
    private String address;

    /**
     * 楼栋结构
     */
    private String nodeStruct;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;


}
