package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.UserNodeRelBo;
import org.dromara.pdkj.domain.vo.UserNodeRelVo;
import org.dromara.pdkj.service.IUserNodeRelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 住户管理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/nodeUserRel")
public class NodeUserRelController extends BaseController {

    private final IUserNodeRelService userNodeRelService;

    /**
     * 查询住户管理列表
     */
    @SaCheckPermission("pdkj:nodeUserRel:list")
    @GetMapping("/list")
    public TableDataInfo<UserNodeRelVo> list(UserNodeRelBo bo, PageQuery pageQuery) {
        return userNodeRelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出住户管理列表
     */
    @SaCheckPermission("pdkj:nodeUserRel:export")
    @Log(title = "住户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserNodeRelBo bo, HttpServletResponse response) {
        List<UserNodeRelVo> list = userNodeRelService.queryList(bo);
        ExcelUtil.exportExcel(list, "住户管理", UserNodeRelVo.class, response);
    }

    /**
     * 获取住户管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:nodeUserRel:query")
    @GetMapping("/{id}")
    public R<UserNodeRelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(userNodeRelService.queryById(id));
    }

    /**
     * 新增住户管理
     */
    @SaCheckPermission("pdkj:nodeUserRel:add")
    @Log(title = "住户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UserNodeRelBo bo) {
        return toAjax(userNodeRelService.insertByBo(bo));
    }

    /**
     * 修改住户管理
     */
    @SaCheckPermission("pdkj:nodeUserRel:edit")
    @Log(title = "住户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserNodeRelBo bo) {
        return toAjax(userNodeRelService.updateByBo(bo));
    }

    /**
     * 删除住户管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:nodeUserRel:remove")
    @Log(title = "住户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(userNodeRelService.deleteWithValidByIds(List.of(ids), true));
    }
}
