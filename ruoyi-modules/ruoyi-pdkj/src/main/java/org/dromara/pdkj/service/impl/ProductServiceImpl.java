package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.Product;
import org.dromara.pdkj.domain.ThingsModel;
import org.dromara.pdkj.domain.bo.ChangeProductStatusBo;
import org.dromara.pdkj.domain.bo.ProductBo;
import org.dromara.pdkj.domain.vo.CategoryVo;
import org.dromara.pdkj.domain.vo.ProductVo;
import org.dromara.pdkj.mapper.CategoryMapper;
import org.dromara.pdkj.mapper.DeviceMapper;
import org.dromara.pdkj.mapper.ProductMapper;
import org.dromara.pdkj.mapper.ThingsModelMapper;
import org.dromara.pdkj.service.IDeviceService;
import org.dromara.pdkj.service.IProductService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 产品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@RequiredArgsConstructor
@Service
public class ProductServiceImpl implements IProductService {

    private final ProductMapper baseMapper;
    private final CategoryMapper categoryMapper;
    private final DeviceMapper deviceMapper;
    private final ThingsModelMapper thingsModelMapper;
    private final IDeviceService deviceService;

    /**
     * 查询产品管理
     *
     * @param id 主键
     * @return 产品管理
     */
    @Override
    public ProductVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询产品管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品管理分页列表
     */
    @Override
    public TableDataInfo<ProductVo> queryPageList(ProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Product> lqw = buildQueryWrapper(bo);
        Page<ProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品管理列表
     *
     * @param bo 查询条件
     * @return 产品管理列表
     */
    @Override
    public List<ProductVo> queryList(ProductBo bo) {
        LambdaQueryWrapper<Product> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Product> buildQueryWrapper(ProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Product::getId);
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Product::getProductName, bo.getProductName());
        lqw.eq(bo.getCategoryId() != null, Product::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getStatus() != null, Product::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品管理
     *
     * @param bo 产品管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductBo bo) {
        Product add = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改产品管理
     *
     * @param bo 产品管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductBo bo) {
        Product update = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Product entity) {
        if (entity.getCategoryId() != null) {
            CategoryVo categoryVo = categoryMapper.selectVoById(entity.getCategoryId());
            if (categoryVo == null) {
                throw new ServiceException("产品分类不存在");
            }
            entity.setCategoryName(categoryVo.getCategoryName());
        }

        Product product = baseMapper.selectOne(new LambdaQueryWrapper<Product>().eq(Product::getProductSymbol, entity.getProductSymbol()).last("limit 1"));
        if (product != null && entity.getId() != product.getId()) {
            throw new ServiceException("产品[" + entity.getProductSymbol() + "]标识已存在");
        }
    }

    /**
     * 校验并批量删除产品管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean changeProductStatus(ChangeProductStatusBo model) {

        if (model.getStatus() != 1 && model.getStatus() != 2) {
            throw new ServiceException("状态更新失败,状态值有误");
        }
        if (model.getStatus() == 2) {
            // 产品下必须包含物模型
            Long thingsCount = thingsModelMapper.selectCount(new LambdaQueryWrapper<ThingsModel>().eq(ThingsModel::getProductId, model.getId()));
            if (thingsCount == 0 && model.getDeviceType() != 3) {
                throw new ServiceException("发布失败，请先添加产品的物模型");
            } else if (thingsCount > 0) {
//             批量更新产品下所有设备的物模型值
                updateDeviceStatusByProductIdAsync(model.getId());
            }
            // TODO 增加modbus之后，产品下子设备的物模型唯一
//            int repeatCount = productMapper.thingsRepeatCountInProduct(model.getProductId());
//            if (repeatCount > 1) {
//                return AjaxResult.error("发布失败，产品物模型的标识符必须唯一");
//            }
        }

        Product product = new Product();
        product.setId(model.getId());
        product.setStatus(model.getStatus());
        return baseMapper.updateById(product) > 0;

    }

    /***
     * 更新产品下所有设备的物模型值
     * @param productId
     */
    @Async
    public void updateDeviceStatusByProductIdAsync(Long productId) {
        List<Device> deviceNumbers = deviceMapper.selectList(new LambdaQueryWrapper<Device>()
            .select(Device::getDeviceCode).eq(Device::getProductId, productId));
        deviceNumbers.forEach(x -> {
            // 缓存新的物模型值
            deviceService.cacheDeviceStatus(productId, x.getDeviceCode());
        });
    }


    @Override
    public ProductVo getProductBySerialNumber(String deviceCode) {

        Device device = deviceMapper.selectOne(new LambdaQueryWrapper<Device>().eq(Device::getDeviceCode, deviceCode).last("limit 1"));
        if (device == null) {
            throw new ServiceException("设备不存在");
        }
        return baseMapper.selectVoById(device.getProductId());
    }
}
