package org.dromara.base.util;

import io.netty.channel.Channel;
import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.mq.bo.DeviceStatusBo;
import org.dromara.common.core.utils.DateUtils;

import java.net.InetSocketAddress;

/**
 * 设备信息工具类
 * <AUTHOR>
 */
public class DeviceUtils {




    /*构造返回MQ的设备状态model*/
    public static DeviceStatusBo buildStatusMsg(Channel channel, String clientId, DeviceStatus status, String ip){
        InetSocketAddress address = (InetSocketAddress) channel.remoteAddress();
        return DeviceStatusBo.builder()
                .deviceCode(clientId)
                .status(status)
                .ip(ip)
                .hostName(address.getHostName())
                .port(address.getPort())
                .timestamp(DateUtils.getNowDate()).build();
    }
}
