package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.pdkj.domain.ProductAuthorize;
import org.hibernate.validator.constraints.Range;

/**
 * 产品授权码业务对象 tb_product_authorize
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@AutoMapper(target = ProductAuthorize.class, reverseConvertGenerate = false)
public class ProductAuthorizeAddBo {

    /**
     * 授权码ID
     */
    @NotNull(message = "授权码数量不能为空")
    @Range(min = 1, max = 1000, message = "一次只能生成1-1000个授权码")
    private Integer authorizeCodeCount;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    /**
     * 备注
     */
    private String remark;


}
