package org.dromara.pdkj.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.Category;
import org.dromara.pdkj.domain.vo.CategoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface CategoryMapper extends BaseMapperPlus<Category, CategoryVo> {
    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default List<CategoryVo> selectVoList(Wrapper<Category> wrapper) {
        return BaseMapperPlus.super.selectVoList(wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default <P extends IPage<CategoryVo>> P selectVoPage(IPage<Category> page, Wrapper<Category> wrapper) {
        return BaseMapperPlus.super.selectVoPage(page, wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    int updateById(@Param(Constants.ENTITY) Category entity);
}
