package org.dromara.mqttclient;

import jakarta.annotation.Resource;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.gateway.mq.TopicsPost;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * mqtt客户端回调
 */
@Slf4j
@Component
@Data
@NoArgsConstructor
public class PubMqttCallBack implements MqttCallbackExtended {
    /**
     * mqtt客户端
     */
    private MqttAsyncClient client;
    /**
     * 创建客户端参数
     */
    private MqttConnectOptions options;

    @Resource
    private TopicsUtils topicsUtils;

    private Boolean enabled;

    private IMqttMessageListener listener;


    public PubMqttCallBack(MqttAsyncClient client, MqttConnectOptions options, TopicsUtils topicsUtils, Boolean enabled, IMqttMessageListener listener) {
        this.client = client;
        this.options = options;
        this.topicsUtils = topicsUtils;
        this.enabled = enabled;
        this.listener = listener;
    }

    /**
     * mqtt客户端连接
     *
     * @param cause 错误
     */
    @Override
    public void connectionLost(Throwable cause) {
        // 连接丢失后，一般在这里面进行重连
        log.debug("=>mqtt 连接丢失", cause);
        int count = 1;
        // int sleepTime = 0;
        boolean willConnect = true;
        while (willConnect) {
            try {
                Thread.sleep(1000);
                log.debug("=>连接[{}]断开，尝试重连第{}次", this.client.getServerURI(), count++);
                this.client.connect(this.options);
                log.debug("=>重连成功");
                willConnect = false;
            } catch (Exception e) {
                log.error("=>重连异常", e);
            }
        }
    }

    /**
     * 客户端订阅主题回调消息
     *
     * @param topic   主题
     * @param message 消息
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        // subscribe后得到的消息会执行到这里面
        try {
            listener.messageArrived(topic, message);
        } catch (Exception e) {
            log.warn("mqtt 订阅消息异常", e);
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {

    }


    /**
     * 监听mqtt连接消息
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT内部客户端已经连接!");
        System.out.print(" * * * * * * * * * * * * 智能家居SAAS平台[✔启动成功] * * * * * * * * * * * *        \n");

        //连接后订阅, enable为false表示使用emq
        if (!enabled) {
            try {
                TopicsPost allPost = topicsUtils.getAllPost();
                client.subscribe(allPost.getTopics(), allPost.getQos());
                log.info("mqtt监控主题,{}", Arrays.asList(allPost.getTopics()));
            } catch (MqttException e) {
                log.error("=>订阅主题失败 error={}", e.getMessage());
            }
        }
    }
}
