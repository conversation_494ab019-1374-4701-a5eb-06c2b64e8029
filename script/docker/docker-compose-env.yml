volumes:
  postgres-volume:
  redis-data:
services:
  postgres:
    image: bitnami/postgresql:17.4.0
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
    ports:
      - "5432:5432"
    volumes:
      - postgres-volume:/var/lib/postgresql/data
  redis:
    image: redis:6.2.12
    ports:
      - 6379:6379
    command: redis-server --requirepass 123456
    volumes:
      - redis-data:/data
