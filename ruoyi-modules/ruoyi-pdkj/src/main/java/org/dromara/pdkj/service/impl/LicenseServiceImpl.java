package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.License;
import org.dromara.pdkj.domain.bo.LicenseBo;
import org.dromara.pdkj.domain.vo.LicenseVo;
import org.dromara.pdkj.mapper.LicenseMapper;
import org.dromara.pdkj.service.ILicenseService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 尚云p2pService业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RequiredArgsConstructor
@Service
public class LicenseServiceImpl implements ILicenseService {

    private final LicenseMapper baseMapper;

    /**
     * 查询尚云p2p
     *
     * @param id 主键
     * @return 尚云p2p
     */
    @Override
    public LicenseVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询尚云p2p列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 尚云p2p分页列表
     */
    @Override
    public TableDataInfo<LicenseVo> queryPageList(LicenseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<License> lqw = buildQueryWrapper(bo);
        Page<LicenseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的尚云p2p列表
     *
     * @param bo 查询条件
     * @return 尚云p2p列表
     */
    @Override
    public List<LicenseVo> queryList(LicenseBo bo) {
        LambdaQueryWrapper<License> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<License> buildQueryWrapper(LicenseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<License> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(License::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDid()), License::getDid, bo.getDid());
        lqw.eq(StringUtils.isNotBlank(bo.getLicense()), License::getLicense, bo.getLicense());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), License::getDeviceCode, bo.getDeviceCode());
        return lqw;
    }

    /**
     * 新增尚云p2p
     *
     * @param bo 尚云p2p
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LicenseBo bo) {
        License add = MapstructUtils.convert(bo, License.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改尚云p2p
     *
     * @param bo 尚云p2p
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LicenseBo bo) {
        License update = MapstructUtils.convert(bo, License.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(License entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除尚云p2p信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
