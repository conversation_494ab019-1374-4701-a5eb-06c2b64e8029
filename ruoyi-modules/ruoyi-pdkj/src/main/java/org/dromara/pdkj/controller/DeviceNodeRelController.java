package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.DeviceNodeRelBo;
import org.dromara.pdkj.domain.vo.DeviceNodeRelVo;
import org.dromara.pdkj.service.IDeviceNodeRelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备楼栋关联
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/nodeRel")
public class DeviceNodeRelController extends BaseController {

    private final IDeviceNodeRelService deviceNodeRelService;

    /**
     * 查询设备楼栋关联列表
     */
    @SaCheckPermission("pdkj:nodeRel:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceNodeRelVo> list(DeviceNodeRelBo bo, PageQuery pageQuery) {
        return deviceNodeRelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备楼栋关联列表
     */
    @SaCheckPermission("pdkj:nodeRel:export")
    @Log(title = "设备楼栋关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceNodeRelBo bo, HttpServletResponse response) {
        List<DeviceNodeRelVo> list = deviceNodeRelService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备楼栋关联", DeviceNodeRelVo.class, response);
    }

    /**
     * 获取设备楼栋关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:nodeRel:query")
    @GetMapping("/{id}")
    public R<DeviceNodeRelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(deviceNodeRelService.queryById(id));
    }

    /**
     * 新增设备楼栋关联
     */
    @SaCheckPermission("pdkj:nodeRel:add")
    @Log(title = "设备楼栋关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceNodeRelBo bo) {
        return toAjax(deviceNodeRelService.insertByBo(bo));
    }

    /**
     * 修改设备楼栋关联
     */
    @SaCheckPermission("pdkj:nodeRel:edit")
    @Log(title = "设备楼栋关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceNodeRelBo bo) {
        return toAjax(deviceNodeRelService.updateByBo(bo));
    }

    /**
     * 删除设备楼栋关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:nodeRel:remove")
    @Log(title = "设备楼栋关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(deviceNodeRelService.deleteWithValidByIds(List.of(ids), true));
    }
}
