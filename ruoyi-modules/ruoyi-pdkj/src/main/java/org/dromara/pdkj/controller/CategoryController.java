package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.CategoryBo;
import org.dromara.pdkj.domain.vo.CategoryVo;
import org.dromara.pdkj.service.ICategoryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品类型
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/category")
public class CategoryController extends BaseController {

    private final ICategoryService categoryService;

    /**
     * 查询产品类型列表
     */
    @SaCheckPermission("pdkj:category:list")
    @GetMapping("/list")
    public TableDataInfo<CategoryVo> list(CategoryBo bo, PageQuery pageQuery) {
        return categoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询所有产品类型列表
     */
    @SaCheckPermission("pdkj:category:list")
    @GetMapping("/list/all")
    public R<List<CategoryVo>> listAll(CategoryBo bo) {
        return R.ok(categoryService.queryList(bo));
    }

    /**
     * 导出产品类型列表
     */
    @SaCheckPermission("pdkj:category:export")
    @Log(title = "产品类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CategoryBo bo, HttpServletResponse response) {
        List<CategoryVo> list = categoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品类型", CategoryVo.class, response);
    }

    /**
     * 获取产品类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:category:query")
    @GetMapping("/{id}")
    public R<CategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(categoryService.queryById(id));
    }

    /**
     * 新增产品类型
     */
    @SaCheckPermission("pdkj:category:add")
    @Log(title = "产品类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CategoryBo bo) {
        return toAjax(categoryService.insertByBo(bo));
    }

    /**
     * 修改产品类型
     */
    @SaCheckPermission("pdkj:category:edit")
    @Log(title = "产品类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CategoryBo bo) {
        return toAjax(categoryService.updateByBo(bo));
    }

    /**
     * 删除产品类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:category:remove")
    @Log(title = "产品类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(categoryService.deleteWithValidByIds(List.of(ids), true));
    }
}
