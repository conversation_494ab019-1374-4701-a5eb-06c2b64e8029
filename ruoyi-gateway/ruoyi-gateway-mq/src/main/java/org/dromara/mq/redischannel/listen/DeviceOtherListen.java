package org.dromara.mq.redischannel.listen;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.mq.redischannel.consumer.DeviceOtherMsgConsumer;
import org.dromara.mq.redischannel.queue.DeviceOtherQueue;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/2/28 10:02
 */
@Slf4j
@Component
public class DeviceOtherListen {

    @Resource
    private DeviceOtherMsgConsumer otherMsgConsumer;

    @Async(PdkjConstant.TASK.DEVICE_OTHER_TASK)
    public void listen() {
        while (true) {
            try {
                DeviceReportBo reportBo = DeviceOtherQueue.take();
                otherMsgConsumer.consume(reportBo);
            } catch (Exception e) {
                log.error("=>emq数据转发异常");
            }
        }
    }
}
