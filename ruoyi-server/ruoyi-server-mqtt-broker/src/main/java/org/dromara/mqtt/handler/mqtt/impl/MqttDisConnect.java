package org.dromara.mqtt.handler.mqtt.impl;


import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessage;
import io.netty.handler.codec.mqtt.MqttMessageType;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.mqtt.annotation.Process;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.manager.SessionManger;


@Process(type = MqttMessageType.DISCONNECT)
@Slf4j
public class MqttDisConnect implements MqttHandler {


    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        /*获取clientId*/
        String clientId = AttributeUtils.getClientId(ctx.channel());
        /*获取session*/
        Session session = AttributeUtils.getSession(ctx.channel());
        log.debug("=>客户端正常断开,clientId:[{}]", clientId);
        try {
            if (!session.getConnected()) {
                session.getHandlerContext().close();
                return;
            }
            /*处理断开客户端连接*/
            SessionManger.pingTimeout(session.getClientId());
            /*移除相关topic*/
            ClientManager.remove(session.getClientId());
        } catch (Exception e) {
            log.error("=>客户端断开连接异常:{}", session);
        }
    }
}
