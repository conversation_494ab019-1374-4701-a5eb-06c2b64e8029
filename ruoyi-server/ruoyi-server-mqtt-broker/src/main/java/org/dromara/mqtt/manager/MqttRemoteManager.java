package org.dromara.mqtt.manager;


import io.netty.buffer.Unpooled;
import io.netty.handler.codec.mqtt.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.enums.TopicType;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.dromara.mqtt.model.PushMessageBo;
import org.dromara.mqttclient.PubMqttClient;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;


@Slf4j
@Component
public class MqttRemoteManager {

    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IDeviceService deviceService;
    /**
     * true: 使用netty搭建的mqttBroker  false: 使用emq
     */
    @Value("${server.broker.enabled}")
    private Boolean enabled;

    @Resource
    private PubMqttClient pubMqttClient;

    /**
     * 推送设备状态
     *
     * @param deviceCode 设备
     * @param status     状态
     */
    public void pushDeviceStatus(Long productId, String deviceCode, DeviceStatus status) {
        //兼容emqx推送TCP客户端上线
        DeviceVo device = deviceService.selectDeviceByDeviceCode(deviceCode);
        if (device == null) {
            log.error("设备不存在:{}", deviceCode);
            return;
        }
        String message = "{\"status\":" + status.getType() + ",\"isShadow\":" + device.getIsShadow() + ",\"rssi\":" + device.getRssi() + "}";
        String topic = topicsUtils.buildTopic(device.getProductId(), deviceCode, TopicType.STATUS_POST);
        if (enabled) {
            MqttPublishMessage publishMessage = (MqttPublishMessage) MqttMessageFactory.newMessage(
                new MqttFixedHeader(MqttMessageType.PUBLISH, false, MqttQoS.AT_MOST_ONCE, false, 0),
                new MqttPublishVariableHeader(topic, 0),
                Unpooled.buffer().writeBytes(message.getBytes(StandardCharsets.UTF_8))
            );
            ClientManager.pubTopic(publishMessage);
        } else {
            //emqx直接用客户端推送
            pubMqttClient.publish(1, false, topic, message);
        }

    }

    /**
     * 公共推送消息方法
     *
     * @param bo 消息体
     */
    public void pushCommon(PushMessageBo bo) {
        //netty版本发送
        if (enabled) {
            MqttPublishMessage publishMessage = (MqttPublishMessage) MqttMessageFactory.newMessage(
                new MqttFixedHeader(MqttMessageType.PUBLISH, false, MqttQoS.AT_MOST_ONCE, false, 0),
                new MqttPublishVariableHeader(bo.getTopic(), 0),
                Unpooled.buffer().writeBytes(bo.getMessage().getBytes(StandardCharsets.UTF_8))
            );
            ClientManager.pubTopic(publishMessage);
        } else {
            pubMqttClient.publish(0, false, bo.getTopic(), bo.getMessage());
        }
    }
}
