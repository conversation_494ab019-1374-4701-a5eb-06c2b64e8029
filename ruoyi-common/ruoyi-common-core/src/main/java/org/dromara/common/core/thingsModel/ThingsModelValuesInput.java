package org.dromara.common.core.thingsModel;

import lombok.Data;

import java.util.List;

/**
 * 设备输入物模型值参数
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@Data
public class ThingsModelValuesInput {
    /**
     * 产品ID
     **/
    private Long productId;

    private Long deviceId;

    /**
     * 设备ID
     **/
    private String deviceNumber;

    /**
     * 设备物模型值的字符串格式
     **/
    private String stringValue;

    /**
     * 设备物模型值的集合
     **/
    private List<ThingsModelSimpleItem> thingsModelSimpleItem;

    private Integer slaveId;


}
