package org.dromara.collect.base.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ByteUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.netty.buffer.Unpooled;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.collect.base.IDeviceProtocol;
import org.dromara.collect.command.LancenLockCommand;
import org.dromara.collect.model.lancen.LockPushData;
import org.dromara.common.core.annotation.SysProtocol;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.enums.ServerType;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.mq.DeviceReport;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.common.core.mq.message.DeviceData;
import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.common.core.thingsModel.ThingsModelValuesInput;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.redis.utils.RedisKeyBuilder;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

@Slf4j
@Component("lancenProtocolService")
@RequiredArgsConstructor
@SysProtocol(name = "仿揽胜协议", protocolCode = PdkjConstant.PROTOCOL.PDKJ_LANCEN_LOCK_PROTOCAL, description = "系统内置")
public class LancenDeviceProtocolService implements IDeviceProtocol {

    private final IDeviceService deviceService;
    private final Map<String, Map<Integer, byte[]>> JpegBytesWithDeviceCode = new HashMap<>();

    @Override
    public DeviceReport decode(DeviceData deviceData, String clientId) {
        byte[] data = deviceData.getData();
        int command = getCommand(data);
        JSONObject body = getData(command, data);
        log.info("揽胜协议 decode...[{}, {}]", command, body);

        List<ThingsModelSimpleItem> thingsModelSimpleItems = new ArrayList<>();
        // 门铃
        if (command == LancenLockCommand.UNLOCK_LOG_REQ && body.getInt("type") == 1) {
            String fileName = "lock/event/" + DateUtils.datePath() + "/" + IdUtil.fastSimpleUUID();
            // 120超时
            RedisUtils.setCacheObject(RedisKeyBuilder.buildDevicePicNameCacheKey(clientId), fileName, Duration.ofSeconds(120));
            body.set("picUrl", OssFactory.instance().getDomain() + "/" + fileName + ".jpg");
        }

        // 图片类型
        if (command == LancenLockCommand.UPLOAD_PIC_REQ) {
            uploadPic(clientId, data);
            return null;
        }

        byte[] responseData = null;

        String topicName = String.format("/device/%d/%s/event/post", deviceData.getProductId(), clientId);
        if (command == LancenLockCommand.UPLOAD_CONFIGURATION_REQ) {
            // 保存配置
            for (Map.Entry<String, Object> entry : JSONUtil.parseObj(body)) {
                ThingsModelSimpleItem item = new ThingsModelSimpleItem();
                item.setId(entry.getKey());
                item.setValue(String.valueOf(entry.getValue()));
                item.setTs(new Date());
                item.setTimestamp(item.getTs().toString());
                thingsModelSimpleItems.add(item);
            }
            topicName = String.format("/device/%d/%s/function/post", deviceData.getProductId(), clientId);
            responseData = HexUtil.decodeHex("10274d1300000000");
        }

        // 2800000000000000 获取p2p服务器地址
        if (command == LancenLockCommand.GET_CONFIG_REQ) {
            String dataStr = "{\"P2P\":[{\"IP\":\"************\",\"Port\":\"32100\"},{\"IP\":\"*************\",\"Port\":\"32100\"}]}";
            responseData = buildResponse(LancenLockCommand.GET_CONFIG_RES, dataStr.getBytes(StandardCharsets.UTF_8));
        }

        // 2e00000000000000 应该是心跳
        if (command == LancenLockCommand.HEARTBEAT_REQ) {
            responseData = HexUtil.decodeHex("2f00000000000000");
        }

        // 开锁/门铃/报警等，根据type区分
        if (command == LancenLockCommand.UNLOCK_LOG_REQ) {

            String remark = "用户开门";
            String id = "unlock";
            remark = switch (body.getInt("type")) {
                case 1 -> {
                    id = "calling";
                    yield "用户呼叫";
                }
                case 2, 3 -> "预留";
                case 4 -> "低电量";
                case 5, 6 -> {
                    LockPushData pushData = JSONUtil.toBean(Base64.decodeStr(body.getStr("data")), LockPushData.class);
                    LancenLockCommand.OpenLockType openLockType = LancenLockCommand.OpenLockType.byType(pushData.getContent());
                    id = "unlock";
                    if (openLockType == LancenLockCommand.OpenLockType.REMOTE) {
                        yield pushData.getApp_user().startsWith(UserType.SYS_USER.getUserType().toUpperCase()) ? "系统开锁" : "APP用户开锁";
                    }
                    yield "用户" + (StringUtils.isEmpty(pushData.getUser_name()) ? pushData.getUser_id() : pushData.getUser_name()) + openLockType.getDesc();
                }
                case 10 -> "声音预警";
                case 11 -> "SOS";
                case 12 -> "久留预警";
                default -> remark;
            };

            responseData = HexUtil.decodeHex("f00220700000000");
            ThingsModelSimpleItem item = new ThingsModelSimpleItem();
            item.setBit(false);
            item.setId(id);
            item.setValue(1);
            item.setRemark(remark);
            item.setTs(new Date());
            item.setTimestamp(String.valueOf(item.getTs().getTime()));
            thingsModelSimpleItems.add(item);
        }

        // e903000009000000f50000000100000000 设备已准备好开锁
        if (command == LancenLockCommand.UNLOCK_READY_REQ) {
            responseData = HexUtil.decodeHex("6400000000000000");
        }

        ThingsModelValuesInput valueInput = new ThingsModelValuesInput();
        DeviceVo deviceVo = deviceService.selectDeviceRunningStatusByDeviceCode(clientId, null);
        if (deviceVo != null) {
            valueInput.setDeviceId(deviceVo.getId());
        }
        valueInput.setDeviceNumber(deviceData.getDeviceCode());
        valueInput.setProductId(deviceData.getProductId());
        valueInput.setThingsModelSimpleItem(thingsModelSimpleItems);

        // 获取设备配置信息
        if (command == LancenLockCommand.GET_CONFIG_REQ) {
            // 获取设备运行状态
            JSONObject config = new JSONObject();
            if (deviceVo != null) {
                deviceVo.getThingsModels().stream()
                    .filter(thingsModelItem -> thingsModelItem.getType() == ThingsModelType.SERVICE.getCode())
                    .forEach(thingsModelItem -> config.set(thingsModelItem.getId(), thingsModelItem.getShadow()));

                if (body.containsKey("sign")) {
                    // 保存sign
                    Device update = new Device();
                    update.setId(deviceVo.getId());
                    update.setSign(body.getStr("sign"));
                    deviceService.updateById(update);
                }
            }
            if (!config.containsKey("standby_mode")) {
                config.set("standby_mode", 1);
            }
            if (!config.containsKey("screenon_time")) {
                config.set("screenon_time", 10);
            }
            config.set("time", DateUtil.now() + " Zone:+8:00");
            responseData = buildResponse(LancenLockCommand.GET_CONFIG_RES, config.toString().getBytes(StandardCharsets.UTF_8));
        }

        DeviceReport report = new DeviceReport();
        report.setCommand(command);
        report.setClientId(clientId);
        report.setDeviceCode(clientId);
        report.setBody(body);
        report.setValuesInput(valueInput);
        report.setResponseData(responseData);
        report.setProtocolCode(PdkjConstant.PROTOCOL.PDKJ_LANCEN_LOCK_PROTOCAL);
        report.setTopic(topicName);

        return report;
    }

    private void uploadPic(String clientId, byte[] data) {
        // 数据长度
        int dataLen = ByteUtil.bytesToInt(new byte[]{data[4], data[5], 0x00, 0x00});
        int jpegLen = ByteUtil.bytesToInt(new byte[]{data[8], data[9], 0x00, 0x00});
        int chunkIndex = ByteUtil.bytesToInt(new byte[]{data[16], 0x00, 0x00, 0x00});
        byte[] jpegData = new byte[dataLen - 16];
        System.arraycopy(data, 24, jpegData, 0, jpegData.length);
        Map<Integer, byte[]> receiveData = JpegBytesWithDeviceCode.get(clientId);
        if (receiveData == null) {
            receiveData = new HashMap<>();
        }

        Map<Integer, byte[]> map = new HashMap<>();
        map.put(chunkIndex, jpegData);
        receiveData.put(chunkIndex, jpegData);
        JpegBytesWithDeviceCode.put(clientId, receiveData);

        // 数据长度不等于1024就保存图片，这里的1024是锁端定义的每次一块的图片数据
        if (dataLen - 16 != 1024) {
            // 保存图片到文件
            try {
                byte[] bytes = new byte[jpegLen];
                int size = 0;
                for (int i = 0; i < receiveData.size(); i++) {
                    // 块索引从 1 开始
                    byte[] chunk = receiveData.get(i + 1);
                    if (chunk == null) {
                        throw new IOException("缺少块 #" + (i + 1));
                    }
                    System.arraycopy(chunk, 0, bytes, size, chunk.length);
                    size += chunk.length;
                }

                OssClient storage = OssFactory.instance();
                storage.uploadSuffix(bytes, ".jpg", "image/jpeg", RedisUtils.getCacheObject(RedisKeyBuilder.buildDevicePicNameCacheKey(clientId)));

            } catch (Exception e) {
                e.printStackTrace();
            }

            JpegBytesWithDeviceCode.remove(clientId);
        }
    }

    @Override
    public byte[] encode(DeviceData message, String clientId) {

        JSONObject parse = JSONUtil.parseObj(message.getBody());
        if (parse.containsKey("unlock")) {
            JSONObject entries = new JSONObject();
            entries.set("command", 0);
            entries.set("time", DateUtil.now() + " Zone:+8:00");
            entries.set("params", Map.of("app_user", message.getUserType() + ":" + message.getUserId()));
            byte[] bData = HexUtil.decodeHex(HexUtil.encodeHexStr(entries.toString()));
            byte[] bCommand = new byte[]{(byte) 0xea, 0x03, 0x00, 0x00};
            byte[] bLength = ByteUtil.intToBytes(bData.length);
            return ArrayUtil.addAll(bCommand, bLength, bData);
        }

        if (parse.containsKey("upgrade")) {
            return HexUtil.decodeHex("7000000000000000");
        }

        if (parse.containsKey("wake_up")) {
            return HexUtil.decodeHex("0B00000000000000");
        }

        if (parse.containsKey("unbind")) {
            return HexUtil.decodeHex("2B00000000000000");
        }

        if (parse.containsKey("get_power")) {
            return HexUtil.decodeHex("7200000000000000");
        }

        // 配置信息
        byte[] bData = HexUtil.decodeHex(HexUtil.encodeHexStr(parse.toString().getBytes(StandardCharsets.UTF_8)));
        byte[] bCommand = ByteUtil.intToBytes(49);
        byte[] bLength = ByteUtil.intToBytes(bData.length);
        byte[] data = ArrayUtil.addAll(bCommand, bLength, bData);

        log.info("揽胜协议 encode...");
        return data;
    }

    @Override
    public byte[] checkDevice(String clientId) {
        DeviceVo deviceVo = deviceService.selectDeviceByDeviceCode(clientId);
        return deviceVo == null ? HexUtil.decodeHex("2B00000000000000") : null;
    }

    private int getCommand(byte[] data) {
        byte[] command = new byte[]{data[0], data[1], 0x00, 0x00};
        return ByteUtil.bytesToInt(command);
    }

    private JSONObject getData(int command, byte[] data) {
        byte[] realData = new byte[data.length - 8];
        System.arraycopy(data, 8, realData, 0, realData.length);
        String jsonStr = new String(realData);
        if (StringUtils.isEmpty(jsonStr)) {
            return new JSONObject();
        }

        if (command == LancenLockCommand.LOCK_POWER_RES) {
            JSONObject entries = new JSONObject();
            entries.set("power", jsonStr);
            return JSONUtil.parseObj(jsonStr);
        }

        try {
            return JSONUtil.parseObj(jsonStr);
        } catch (Exception e) {
            System.out.println("command -> " + command + " jsonStr = " + jsonStr);
            return new JSONObject();
        }
    }

    private static byte[] buildResponse(int command, byte[] responseData) {
        byte[] bCommand = ByteUtil.intToBytes(command);
        byte[] bLength = ByteUtil.intToBytes(responseData.length);
        return ArrayUtil.addAll(bCommand, bLength, responseData);
    }
}
