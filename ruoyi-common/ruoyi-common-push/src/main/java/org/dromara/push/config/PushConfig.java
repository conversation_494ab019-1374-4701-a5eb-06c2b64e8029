package org.dromara.push.config;


import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.push20160801.AsyncClient;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.push")
public class PushConfig {
    private String accessKeyId;
    private String accessKeySecret;
    private Config ios;
    private Config android;


    @Data
    public static class Config {
        private Long appKey;
        private String appSecret;
        private String env;
    }

    @Bean
    public StaticCredentialProvider provider() {
        return StaticCredentialProvider.create(Credential.builder()
            .accessKeyId(accessKeyId)
            .accessKeySecret(accessKeySecret)
            .build());
    }

    @Bean
    public AsyncClient asyncClient(StaticCredentialProvider provider) {
        return AsyncClient.builder()
            //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
            .credentialsProvider(provider)
            //.serviceConfiguration(Configuration.create()) // Service-level configuration
            // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
            .overrideConfiguration(
                ClientOverrideConfiguration.create()
                    // Endpoint 请参考 https://api.aliyun.com/product/Push
                    .setEndpointOverride("cloudpush.aliyuncs.com")
                //.setConnectTimeout(Duration.ofSeconds(30))
            )
            .build();
    }
}
