package org.dromara.mqtt.service.impl;


import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.collect.command.LancenLockCommand;
import org.dromara.common.core.enums.TopicType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.common.core.thingsModel.ThingsModelValuesInput;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.mq.model.ReportDataBo;
import org.dromara.mq.service.IDataHandler;
import org.dromara.mq.service.IMqttMessagePublish;
import org.dromara.mqtt.manager.MqttRemoteManager;
import org.dromara.mqtt.model.PushMessageBo;
import org.dromara.mqtt.model.lancen.AppNoticeMessage;
import org.dromara.mqtt.model.lancen.Input;
import org.dromara.mqtt.model.lancen.P2plicense;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.EventLog;
import org.dromara.pdkj.domain.License;
import org.dromara.pdkj.domain.UserDeviceRel;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.mapper.DeviceMapper;
import org.dromara.pdkj.mapper.EventLogMapper;
import org.dromara.pdkj.mapper.LicenseMapper;
import org.dromara.pdkj.mapper.UserDeviceRelMapper;
import org.dromara.pdkj.service.IDeviceService;
import org.dromara.push.enums.PushTarget;
import org.dromara.push.enums.PushType;
import org.dromara.push.service.IPushService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 上报数据处理方法集合
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataHandlerImpl implements IDataHandler {

    @Value("${p2p.crcKey}")
    private String crcKey;
    @Value("${p2p.p2pKey}")
    private String p2pKey;
    @Value("${p2p.initString}")
    private String initString;
    @Resource
    private EventLogMapper eventLogMapper;
    @Resource
    private IMqttMessagePublish messagePublish;

    @Resource
    private MqttRemoteManager remoteManager;
    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private MqttRemoteManager mqttRemoteManager;
    @Resource
    private UserDeviceRelMapper userDeviceRelMapper;
    @Resource
    private LicenseMapper licenseMapper;
    @Resource
    private IPushService pushService;

    /**
     * 上报属性或功能处理
     *
     * @param bo 上报数据模型
     */
    @Override
    public void reportData(ReportDataBo bo) {
        try {
            List<ThingsModelSimpleItem> thingsModelSimpleItems = bo.getDataList();
            if (CollectionUtils.isEmpty(bo.getDataList())) {
                thingsModelSimpleItems = JsonUtils.parseArray(bo.getMessage(), ThingsModelSimpleItem.class);
            }
            ThingsModelValuesInput input = new ThingsModelValuesInput();
            input.setProductId(bo.getProductId());
            input.setDeviceNumber(bo.getDeviceCode().toUpperCase());
            input.setThingsModelSimpleItem(thingsModelSimpleItems);
            input.setSlaveId(bo.getSlaveId());
            List<ThingsModelSimpleItem> result = deviceService.reportDeviceThingsModelValue(input, bo.getType(), bo.isShadow());

            //发送至前端
            PushMessageBo messageBo = new PushMessageBo();
            messageBo.setTopic(topicsUtils.buildTopic(bo.getProductId(), bo.getDeviceCode(), TopicType.WS_SERVICE_INVOKE));
            messageBo.setMessage(JsonUtils.toJsonString(result));
            remoteManager.pushCommon(messageBo);

            // 写入到数据库
            DeviceVo deviceVo = deviceService.selectDeviceRunningStatusByDeviceCode(bo.getDeviceCode(), bo.getSlaveId());
            Device update = new Device();
            update.setId(deviceVo.getId());
            update.setThingsModelValue(JSONUtil.parseArray(deviceVo.getThingsModels()));
            deviceMapper.updateById(update);

        } catch (Exception e) {
            log.error("接收属性数据，解析数据时异常 message={},e={}", e.getMessage(), e);
        }
    }


    /**
     * 上报事件
     *
     * @param bo 上报数据模型
     */
    @Override
    public void reportEvent(ReportDataBo bo) {
        try {
            List<ThingsModelSimpleItem> thingsModelSimpleItems = JsonUtils.parseArray(bo.getMessage(), ThingsModelSimpleItem.class);
            DeviceVo device = deviceService.selectDeviceByDeviceCode(bo.getDeviceCode());
            List<EventLog> results = new ArrayList<>();
            JSONObject body = new JSONObject();
            if (bo.getBody() != null) {
                body = JSONUtil.parseObj(bo.getBody());
            }
            for (ThingsModelSimpleItem thingsModelSimpleItem : thingsModelSimpleItems) {
                // 添加到设备日志
                EventLog event = new EventLog();
                event.setDeviceId(device.getId());
                event.setDeviceName(device.getDeviceName());
                event.setLogValue(String.valueOf(thingsModelSimpleItem.getValue()));
                event.setRemark(thingsModelSimpleItem.getRemark());
                event.setDeviceCode(device.getDeviceCode());
                event.setIdentity(thingsModelSimpleItem.getId());
                event.setLogType(3);
                event.setIsMonitor(false);
                event.setCreateTime(DateUtils.getNowDate());
                // 1=影子模式，2=在线模式，3=其他
                event.setMode(2);
                event.setPicUrl(body.getStr("picUrl"));
                results.add(event);
                //eventLogService.insertEventLog(event);
            }
            eventLogMapper.insertBatch(results);


            // 推送给app用户
            if (bo.getCommand() == LancenLockCommand.UNLOCK_LOG_REQ) {
                if (LancenLockCommand.PushType.PUSH_TYPE_DOORBELL.getType() == body.getInt("type")) {
                    DeviceVo deviceVo = deviceService.selectDeviceByDeviceCode(bo.getDeviceCode());

                    int screenon_timeout = 60;
                    for (Object o : deviceVo.getThingsModelValue()) {
                        JSONObject jsonObject = (JSONObject) o;
                        Integer timeout = jsonObject.getInt("screenon_timeout");
                        if (timeout != null && timeout > 0) {
                            screenon_timeout = timeout;
                            break;
                        }
                    }
                    License license = licenseMapper.selectOne(new LambdaQueryWrapper<License>()
                        .eq(License::getDeviceCode, bo.getDeviceCode())
                        .last("limit 1")
                    );
                    P2plicense p2plicense = new P2plicense();
                    p2plicense.setDid(license.getDid());
                    p2plicense.setApLicense(license.getLicense());
                    p2plicense.setCrcKey(crcKey);
                    p2plicense.setP2pKey(p2pKey);
                    p2plicense.setCsKey(p2pKey);
                    p2plicense.setInitialString(initString);

                    // 查找绑定该设备的用户
                    List<UserDeviceRel> userDeviceRels = userDeviceRelMapper.selectList(
                        new LambdaQueryWrapper<UserDeviceRel>()
                            .select(UserDeviceRel::getUserId)
                            .eq(UserDeviceRel::getDeviceId, deviceVo.getId())
                    );

                    AppNoticeMessage noticeMessage = new AppNoticeMessage();
                    noticeMessage.setSn(RandomUtil.randomNumbers(16));
                    noticeMessage.setTime(System.currentTimeMillis());
                    noticeMessage.setExpiredAt(noticeMessage.getTime() + screenon_timeout * 1000L); // 20s后过期
                    noticeMessage.setCmd("makeCall");
                    noticeMessage.setSourceType(bo.getProductSymbol());
                    Input input = new Input();
                    input.setContent("收到门锁视频呼叫，请及时查看");
                    input.setTitle("门锁视频呼叫");
                    input.setSourceDeviceCode(bo.getDeviceCode());
                    input.setSourceImg(body.getStr("picUrl"));
                    input.setWaitTimeOut(screenon_timeout);
                    input.setP2plicense(p2plicense);
                    noticeMessage.setInput(input);
                    userDeviceRels.forEach(userDeviceRel -> {
//                        PushMessageBo pushContent = new PushMessageBo();
//                        pushContent.setTopic("/app/" + userDeviceRel.getUserId() + "/notice/get");
//                        pushContent.setMessage(JSONUtil.toJsonStr(noticeMessage));
//                        mqttRemoteManager.pushCommon(pushContent);
                        pushService.push2ios(
                            PushType.NOTICE, PushTarget.ALIAS,
                            "userid:" + userDeviceRel.getUserId(),
                            "收到门锁视频呼叫，请及时查看",
                            JSONUtil.toJsonStr(noticeMessage)
                        );

                    });

                }
            }
        } catch (Exception e) {
            log.error("接收事件，解析数据时异常 message={}", e.getMessage());
        }
    }

    /**
     * 上报设备信息
     */
    @Override
    public void reportDevice(ReportDataBo bo) {
        try {
            // 设备实体
            DeviceVo deviceVo = deviceService.selectDeviceByDeviceCode(bo.getDeviceCode());
            // 上报设备信息
            Device device = JsonUtils.parseObject(bo.getMessage(), Device.class);
            device.setProductId(bo.getProductId());
            device.setDeviceCode(bo.getDeviceCode());
            deviceService.reportDevice(device, deviceVo);
            // 发布设备状态
            messagePublish.publishStatus(bo.getProductId(), bo.getDeviceCode(), 3, deviceVo.getIsShadow(), device.getRssi());
        } catch (Exception e) {
            log.error("接收设备信息，解析数据时异常 message={}", e.getMessage());
            throw new ServiceException(e.getMessage(), 1);
        }
    }


}
