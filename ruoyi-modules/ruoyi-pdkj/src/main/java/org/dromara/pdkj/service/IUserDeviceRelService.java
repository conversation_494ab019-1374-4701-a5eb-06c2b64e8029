package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.UserDeviceRelBo;
import org.dromara.pdkj.domain.vo.UserDeviceRelVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户设备Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IUserDeviceRelService {

    /**
     * 查询用户设备
     *
     * @param id 主键
     * @return 用户设备
     */
    UserDeviceRelVo queryById(Long id);

    /**
     * 分页查询用户设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户设备分页列表
     */
    TableDataInfo<UserDeviceRelVo> queryPageList(UserDeviceRelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户设备列表
     *
     * @param bo 查询条件
     * @return 用户设备列表
     */
    List<UserDeviceRelVo> queryList(UserDeviceRelBo bo);

    /**
     * 新增用户设备
     *
     * @param bo 用户设备
     * @return 是否新增成功
     */
    Boolean insertByBo(UserDeviceRelBo bo);

    /**
     * 修改用户设备
     *
     * @param bo 用户设备
     * @return 是否修改成功
     */
    Boolean updateByBo(UserDeviceRelBo bo);

    /**
     * 校验并批量删除用户设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
