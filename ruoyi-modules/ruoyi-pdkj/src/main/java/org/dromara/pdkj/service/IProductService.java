package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.ChangeProductStatusBo;
import org.dromara.pdkj.domain.bo.ProductBo;
import org.dromara.pdkj.domain.vo.ProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface IProductService {

    /**
     * 查询产品管理
     *
     * @param id 主键
     * @return 产品管理
     */
    ProductVo queryById(Long id);

    /**
     * 分页查询产品管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品管理分页列表
     */
    TableDataInfo<ProductVo> queryPageList(ProductBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品管理列表
     *
     * @param bo 查询条件
     * @return 产品管理列表
     */
    List<ProductVo> queryList(ProductBo bo);

    /**
     * 新增产品管理
     *
     * @param bo 产品管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductBo bo);

    /**
     * 修改产品管理
     *
     * @param bo 产品管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductBo bo);

    /**
     * 校验并批量删除产品管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean changeProductStatus(ChangeProductStatusBo bo);

    ProductVo getProductBySerialNumber(String deviceCode);
}
