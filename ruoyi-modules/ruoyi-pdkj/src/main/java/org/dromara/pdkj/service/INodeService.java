package org.dromara.pdkj.service;

import cn.hutool.core.lang.tree.Tree;
import org.dromara.pdkj.domain.bo.NodeAddBo;
import org.dromara.pdkj.domain.bo.NodeBo;
import org.dromara.pdkj.domain.vo.NodeVo;

import java.util.Collection;
import java.util.List;

/**
 * 楼栋管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface INodeService {

    /**
     * 查询楼栋管理
     *
     * @param id 主键
     * @return 楼栋管理
     */
    NodeVo queryById(Long id);


    /**
     * 查询符合条件的楼栋管理列表
     *
     * @param bo 查询条件
     * @return 楼栋管理列表
     */
    List<NodeVo> queryList(NodeBo bo);

    /**
     * 新增楼栋管理
     *
     * @param bo 楼栋管理
     * @return 是否新增成功
     */
    Boolean insertByBo(NodeAddBo bo);

    /**
     * 修改楼栋管理
     *
     * @param bo 楼栋管理
     * @return 是否修改成功
     */
    Boolean updateByBo(NodeBo bo);

    /**
     * 校验并批量删除楼栋管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<Tree<Long>> queryTree(NodeBo bo);
}
