package org.dromara.mq.service;


import org.dromara.common.core.mq.DeviceReport;
import org.dromara.common.core.mq.bo.DeviceReportBo;

/**
 * 处理设备上报数据解析
 * <AUTHOR>
 * @date 2022/10/10 13:48
 */
public interface IDeviceReportMessageService {

    /**
     * 处理设备主动上报数据
     * @param bo
     */
    public void parseReportMsg(DeviceReportBo bo);

    /**
     * 构建消息
     * @param bo
     */
//    public Device buildReport(DeviceReportBo bo);


    /**
     * 处理设备主动上报属性
     *
     * @param topicName
     * @param message
     */
    public void handlerReportMessage(DeviceReport message, String topicName);

}
