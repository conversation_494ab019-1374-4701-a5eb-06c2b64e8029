package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.ImportThingsModelBo;
import org.dromara.pdkj.domain.bo.ThingsModelBo;
import org.dromara.pdkj.domain.vo.ThingsModelVo;
import org.dromara.pdkj.service.IThingsModelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物模型
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/thingsModel")
public class ThingsModelController extends BaseController {

    private final IThingsModelService thingsModelService;

    /**
     * 查询物模型列表
     */
    @SaCheckPermission("pdkj:thingsModel:list")
    @GetMapping("/list")
    public TableDataInfo<ThingsModelVo> list(ThingsModelBo bo, PageQuery pageQuery) {
        return thingsModelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出物模型列表
     */
    @SaCheckPermission("pdkj:thingsModel:export")
    @Log(title = "物模型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ThingsModelBo bo, HttpServletResponse response) {
        List<ThingsModelVo> list = thingsModelService.queryList(bo);
        ExcelUtil.exportExcel(list, "物模型", ThingsModelVo.class, response);
    }

    /**
     * 获取物模型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:thingsModel:query")
    @GetMapping("/{id}")
    public R<ThingsModelVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(thingsModelService.queryById(id));
    }

    /**
     * 新增物模型
     */
    @SaCheckPermission("pdkj:thingsModel:add")
    @Log(title = "物模型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ThingsModelBo bo) {
        return toAjax(thingsModelService.insertByBo(bo));
    }

    @SaCheckPermission("pdkj:thingsModel:add")
    @Log(title = "导入物模型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/import")
    public R<String> importByTemplateIds(@Validated(AddGroup.class) @RequestBody ImportThingsModelBo bo) {
        int repeatCount = thingsModelService.importByTemplateIds(bo);
        if (repeatCount == 0) {
            return R.ok("数据导入成功");
        } else {
            return R.fail(repeatCount + "条数据未导入，标识符重复");
        }
    }

    /**
     * 修改物模型
     */
    @SaCheckPermission("pdkj:thingsModel:edit")
    @Log(title = "物模型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ThingsModelBo bo) {
        return toAjax(thingsModelService.updateByBo(bo));
    }

    /**
     * 删除物模型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:thingsModel:remove")
    @Log(title = "物模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(thingsModelService.deleteWithValidByIds(List.of(ids), true));
    }
}
