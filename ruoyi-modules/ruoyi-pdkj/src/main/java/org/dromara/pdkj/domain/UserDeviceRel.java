package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 用户设备对象 user_device_rel
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("\"user\".\"user_device_rel\"")
public class UserDeviceRel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id")
    private Long id;

    /**
     * $column.columnComment
     */
    private Long deviceId;

    /**
     * $column.columnComment
     */
    private Long userId;

    /**
     * $column.columnComment
     */
    private Long roleType;

    /**
     * 设备code
     */
    private String deviceCode;


}
