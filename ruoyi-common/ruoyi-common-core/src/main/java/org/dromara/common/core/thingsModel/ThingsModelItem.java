package org.dromara.common.core.thingsModel;

import lombok.Data;

@Data
public class ThingsModelItem {
    /**
     * 物模型唯一标识符
     */
    private String id;
    /**
     * 物模型名称
     */
    private String name;
    /**
     * 物模型值
     */
    private Object value;
    /**
     * 影子值
     */
    private Object shadow;
    /**
     * 是否首页显示（0-否，1-是）
     */
    private Boolean isChart;
    /**
     * 是否实时监测（0-否，1-是）
     */
    private Boolean isMonitor;
    /**
     * 是否实时监测（0-否，1-是）
     */
    private Boolean isReadonly;
    /**
     * 是否历史存储（0-否，1-是）
     */
    private Boolean isHistory;
    /**
     * 是否设备分享权限（0-否，1-是）
     */
    private Boolean isSharePerm;
    /**
     * 类型 1=属性，2=功能，3=事件
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer order;
    /**
     * 数据类型
     */
    private Datatype datatype;

    /**
     * 子设备编号
     */
    private Integer slaveId;

    private String regId;

    private String ts;

    public ThingsModelItem() {
        value = "";
        shadow = "";
        order = 0;
        isMonitor = false;
        isHistory = false;
        isReadonly = false;
        isChart = false;
        isSharePerm = false;
    }

}
