package org.dromara.pdkj.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.thingsModel.Specs;
import org.dromara.common.redis.utils.RedisKeyBuilder;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.pdkj.domain.vo.DeviceLogVo;
import org.dromara.pdkj.service.IDeviceRuntimeService;
import org.dromara.pdkj.service.IThingsModelService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class DeviceRuntimeServiceImpl implements IDeviceRuntimeService {

    private final IThingsModelService thingsModelService;

    @Override
    public List<DeviceLogVo> runState(String deviceCode, ThingsModelType type, Long productId, Integer slaveId) {
        //获取redis中的物模型
        String thingsModel = thingsModelService.getCacheThingsModelByProductId(productId);
        JSONObject thingModelObject = JSONUtil.parseObj(thingsModel);
        String key = type == ThingsModelType.PROP ? "properties" : "functions";
        String properties = thingModelObject.getStr(key);
        /*在redis中查找实时数据*/
        String cacheKey = RedisKeyBuilder.buildDeviceRtCacheKey(deviceCode);
        /*物模型标识符 -- value*/
        Map<String, String> cacheMap = RedisUtils.getCacheObject(cacheKey);
        /*查询是否有计算公式参数*/
        String paramsKey = RedisKeyBuilder.buildDeviceRtParamsKey(deviceCode);
        Map<String, String> params = RedisUtils.getCacheObject(paramsKey);
        List<DeviceLogVo> results = new ArrayList<>();
        List<Specs> propList = JSONUtil.toList(properties, Specs.class);
        for (Specs specs : propList) {
            if (specs.getSlaveId().equals(slaveId)) {
                DeviceLogVo log = new DeviceLogVo();
                log.setModelName(specs.getName());
                log.setLogType(type.getCode());
                log.setSpecs(specs.getDatatype());
                log.setIdentity(specs.getId());
                log.setDeviceCode(deviceCode);
                log.setSlaveId(specs.getSlaveId());
                log.setIsMonitor(specs.getIsMonitor());
                log.setFormula(specs.getFormula());
                if (!CollectionUtils.isEmpty(cacheMap) && cacheMap.containsKey(specs.getId())) {
                    DeviceLogVo valueLog = JSONUtil.toBean(cacheMap.get(specs.getId()), DeviceLogVo.class);
                    log.setLogValue(valueLog.getLogValue());
                    log.setUpdateTime(valueLog.getCreateTime());
                    log.setCreateTime(valueLog.getCreateTime());
                    //if (log.getFormula() != null && !log.getFormula().equals("")) {
                    //    params.put("%s", log.getLogValue());
                    //    BigDecimal value = CaculateUtils.execute(log.getFormula(), params);
                    //    log.setLogValue(value.toString());
                    //}
                }
                results.add(log);
            }
        }
        List<DeviceLogVo> result = results.stream().
            filter(log -> log.getLogType() == type.getCode()).collect(Collectors.toList());
        for (DeviceLogVo log : result) {
            parseSpecs(log);
        }
        return result;
    }

    /**
     * 解析数据定义specs
     *
     * @param log
     */
    public void parseSpecs(DeviceLogVo log) {
        DeviceLogVo.DataType spesc = new DeviceLogVo.DataType();
        if (null != log.getSpecs() && !log.getSpecs().equals("null")) {
            DeviceLogVo.DataType dataType = JSONUtil.toBean(log.getSpecs(), DeviceLogVo.DataType.class);
            log.setDataType(dataType);
        } else {
            spesc.setType("integer");
            log.setDataType(spesc);
        }
    }

}
