package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.Estate;

/**
 * 小区信息业务对象 tb_estate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Estate.class, reverseConvertGenerate = false)
public class EstateBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String estateName;

    /**
     * 地址
     */
    private String address;

    /**
     * 楼栋结构
     */
    @NotBlank(message = "楼栋结构不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nodeStruct;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;


}
