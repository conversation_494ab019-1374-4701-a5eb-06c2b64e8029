package org.dromara.web.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ApiOpenLockReqBo {

    @NotNull(message = "产品id不能为空")
    private Long productId;

    @NotBlank(message = "设备编号不能为空")
    private String deviceCode;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "用户密钥不能为空")
    private String userKey;
}
