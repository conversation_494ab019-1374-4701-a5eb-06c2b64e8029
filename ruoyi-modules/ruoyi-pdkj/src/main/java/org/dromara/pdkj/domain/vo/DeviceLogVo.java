package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.DeviceLog;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 设备日志视图对象 tb_device_log
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceLog.class)
public class DeviceLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 标识符
     */
    @ExcelProperty(value = "标识符")
    private String identity;

    /**
     * 物模型名称
     */
    @ExcelProperty(value = "物模型名称")
    private String modelName;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_log_type")
    private Integer logType;

    /**
     * 日志值
     */
    @ExcelProperty(value = "日志值")
    private String logValue;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 监测数据
     */
    @ExcelProperty(value = "监测数据", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isMonitor;

    /**
     * 模式
     */
    @ExcelProperty(value = "模式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ios_device_mode")
    private Integer mode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private String specs;

    private DataType dataType;

    private Integer slaveId;
    /**
     * 计算公式
     */
    private String formula;

    private Integer isParams;


    private Date createTime;
    private Date updateTime;

    @Data
    public static class DataType {
        private String type;
        private String falseText;
        private String trueText;
        private Integer maxLength;
        private String arrayType;
        private String unit;
        private BigDecimal min;
        private BigDecimal max;
        private BigDecimal step;
        private List<EnumItem> enumList;

    }

    @Data
    public static class EnumItem {
        private String text;
        private String value;
    }
}
