package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 设备出厂对象 device_inventory
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot.device_inventory")
public class DeviceInventory extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Long delFlag;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品密钥
     */
    private String deviceSecret;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备别名
     */
    private String deviceAlias;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 激活时间
     */
    private Date lastActivateTime;

    /**
     * 注销时间
     */
    private Date lastDeactivateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 绑定状态
     */
    private Long bindStatus;


    private String deviceMac;


}
