package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.ChangeProductStatusBo;
import org.dromara.pdkj.domain.bo.ProductBo;
import org.dromara.pdkj.domain.vo.ProductVo;
import org.dromara.pdkj.service.IProductService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品管理
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/product")
public class ProductController extends BaseController {

    private final IProductService productService;

    /**
     * 查询产品管理列表
     */
    @SaCheckPermission("pdkj:product:list")
    @GetMapping("/list")
    public TableDataInfo<ProductVo> list(ProductBo bo, PageQuery pageQuery) {
        return productService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询产品管理列表
     */
    @SaCheckPermission("pdkj:product:list")
    @GetMapping("/list/all")
    public R<List<ProductVo>> listAll(ProductBo bo) {
        return R.ok(productService.queryList(bo));
    }

    /**
     * 导出产品管理列表
     */
    @SaCheckPermission("pdkj:product:export")
    @Log(title = "产品管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductBo bo, HttpServletResponse response) {
        List<ProductVo> list = productService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品管理", ProductVo.class, response);
    }

    /**
     * 获取产品管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:product:query")
    @GetMapping("/{id}")
    public R<ProductVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        return R.ok(productService.queryById(id));
    }

    /**
     * 新增产品管理
     */
    @SaCheckPermission("pdkj:product:add")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductBo bo) {
        return toAjax(productService.insertByBo(bo));
    }

    /**
     * 修改产品管理
     */
    @SaCheckPermission("pdkj:product:edit")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductBo bo) {
        return toAjax(productService.updateByBo(bo));
    }


    /**
     * 发布产品
     */
    @SaCheckPermission("pdkj:product:edit")
    @Log(title = "更新产品状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/status")
    public R<Void> changeProductStatus(@RequestBody ChangeProductStatusBo bo) {
        return toAjax(productService.changeProductStatus(bo));
    }

    /**
     * 删除产品管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:product:remove")
    @Log(title = "产品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(productService.deleteWithValidByIds(List.of(ids), true));
    }
}
