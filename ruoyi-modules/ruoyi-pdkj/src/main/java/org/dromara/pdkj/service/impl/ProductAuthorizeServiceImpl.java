package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.uuid.IdUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.ProductAuthorize;
import org.dromara.pdkj.domain.bo.ProductAuthorizeAddBo;
import org.dromara.pdkj.domain.bo.ProductAuthorizeBo;
import org.dromara.pdkj.domain.vo.ProductAuthorizeVo;
import org.dromara.pdkj.mapper.ProductAuthorizeMapper;
import org.dromara.pdkj.service.IProductAuthorizeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 产品授权码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RequiredArgsConstructor
@Service
public class ProductAuthorizeServiceImpl implements IProductAuthorizeService {

    private final ProductAuthorizeMapper baseMapper;

    /**
     * 查询产品授权码
     *
     * @param id 主键
     * @return 产品授权码
     */
    @Override
    public ProductAuthorizeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询产品授权码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品授权码分页列表
     */
    @Override
    public TableDataInfo<ProductAuthorizeVo> queryPageList(ProductAuthorizeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductAuthorize> lqw = buildQueryWrapper(bo);
        Page<ProductAuthorizeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品授权码列表
     *
     * @param bo 查询条件
     * @return 产品授权码列表
     */
    @Override
    public List<ProductAuthorizeVo> queryList(ProductAuthorizeBo bo) {
        LambdaQueryWrapper<ProductAuthorize> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductAuthorize> buildQueryWrapper(ProductAuthorizeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductAuthorize> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductAuthorize::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getAuthorizeCode()), ProductAuthorize::getAuthorizeCode, bo.getAuthorizeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), ProductAuthorize::getDeviceCode, bo.getDeviceCode());
        lqw.eq(bo.getStatus() != null, ProductAuthorize::getStatus, bo.getStatus());
        lqw.eq(bo.getProductId() != null, ProductAuthorize::getProductId, bo.getProductId());
        return lqw;
    }

    /**
     * 新增产品授权码
     *
     * @param bo 产品授权码
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductAuthorizeBo bo) {
        ProductAuthorize add = MapstructUtils.convert(bo, ProductAuthorize.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改产品授权码
     *
     * @param bo 产品授权码
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductAuthorizeBo bo) {
        ProductAuthorize update = MapstructUtils.convert(bo, ProductAuthorize.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductAuthorize entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除产品授权码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean createProductAuthorizeByNum(ProductAuthorizeAddBo bo) {

        Long productId = bo.getProductId();
        int createNum = bo.getAuthorizeCodeCount();
        List<ProductAuthorize> list = new ArrayList<>(createNum);
        for (int i = 0; i < createNum; i++) {
            ProductAuthorize authorize = new ProductAuthorize();
            // 1=未使用，2=使用中
            authorize.setStatus(1);
            authorize.setProductId(productId);
            authorize.setCreateTime(DateUtils.getNowDate());
            authorize.setAuthorizeCode(IdUtils.fastSimpleUUID().toUpperCase());
            list.add(authorize);
        }
        return baseMapper.insertBatch(list);
    }
}
