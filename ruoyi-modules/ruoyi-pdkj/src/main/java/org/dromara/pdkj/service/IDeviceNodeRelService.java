package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.DeviceNodeRelBo;
import org.dromara.pdkj.domain.vo.DeviceNodeRelVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备楼栋关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface IDeviceNodeRelService {

    /**
     * 查询设备楼栋关联
     *
     * @param id 主键
     * @return 设备楼栋关联
     */
    DeviceNodeRelVo queryById(Long id);

    /**
     * 分页查询设备楼栋关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备楼栋关联分页列表
     */
    TableDataInfo<DeviceNodeRelVo> queryPageList(DeviceNodeRelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备楼栋关联列表
     *
     * @param bo 查询条件
     * @return 设备楼栋关联列表
     */
    List<DeviceNodeRelVo> queryList(DeviceNodeRelBo bo);

    /**
     * 新增设备楼栋关联
     *
     * @param bo 设备楼栋关联
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceNodeRelBo bo);

    /**
     * 修改设备楼栋关联
     *
     * @param bo 设备楼栋关联
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceNodeRelBo bo);

    /**
     * 校验并批量删除设备楼栋关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
