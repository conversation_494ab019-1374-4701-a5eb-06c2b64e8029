package org.dromara.ai.component;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.ai.convert.AiDeviceMapper;
import org.dromara.ai.domain.vo.AiDeviceVo;
import org.dromara.common.core.mq.InvokeReqDto;
import org.dromara.mq.service.IFunctionInvoke;
import org.dromara.pdkj.domain.bo.DeviceBo;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Description;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Configuration
@RequiredArgsConstructor
public class DeviceTools {

    @Resource
    private final IDeviceService deviceService;
    @Resource
    private final IFunctionInvoke functionInvoke;


    public record ListDeviceRequest() {
    }

    public record FunctionInvokeRequest(String deviceCode, String identifier, String value) {
    }

    @Bean
    @Description("获取设备列表")
    public Function<ListDeviceRequest, List<AiDeviceVo>> listDevice() {
        return request -> {
            // 获取设备列表
            DeviceBo bo = new DeviceBo();
            List<DeviceVo> deviceVos = deviceService.queryList(bo);
            for (DeviceVo deviceVo : deviceVos) {
                deviceVo.setThingsModels(deviceService.selectDeviceRunningStatusByDeviceCode(deviceVo.getDeviceCode(), deviceVo.getSlaveId()).getThingsModels());
            }
            return AiDeviceMapper.INSTANCE.convertList(deviceVos);
        };
    }

    @Bean
    @Description("根据产品ID,设备编号,物模型控制设备")
    public Function<FunctionInvokeRequest, String> operationDevice() {
        return request -> {
            DeviceVo deviceVo = deviceService.selectDeviceByDeviceCode(request.deviceCode);
            if (deviceVo == null) {
                throw new RuntimeException("设备不存在");
            }

            System.out.println("request = " + JSONUtil.toJsonStr(request));
            InvokeReqDto reqDto = new InvokeReqDto();
            reqDto.setProductId(deviceVo.getProductId());
            reqDto.setDeviceCode(request.deviceCode);
            reqDto.setIdentifier(request.identifier);
            reqDto.setType(2);
            reqDto.setRemoteCommand(Map.of(request.identifier, request.value));
            reqDto.setValue(new JSONObject(Map.of(request.identifier, request.value)));
            System.out.println("reqDto = " + JSONUtil.toJsonStr(reqDto));
            return functionInvoke.invokeNoReply(reqDto);
        };
    }

}
