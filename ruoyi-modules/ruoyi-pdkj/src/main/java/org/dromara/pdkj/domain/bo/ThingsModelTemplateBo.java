package org.dromara.pdkj.domain.bo;

import cn.hutool.json.JSONObject;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.ThingsModelTemplate;

/**
 * 通用物模型业务对象 tb_things_model_template
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ThingsModelTemplate.class, reverseConvertGenerate = false)
public class ThingsModelTemplateBo extends BaseEntity {

    /**
     * 物模型ID
     */
    @NotNull(message = "物模型ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 物模型名称
     */
    @NotBlank(message = "物模型名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String templateName;

    /**
     * 标识符
     */
    @NotBlank(message = "标识符不能为空", groups = {AddGroup.class, EditGroup.class})
    private String identifier;

    /**
     * 模型类别
     */
    @NotNull(message = "模型类别不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String datatype;

    /**
     * 数据定义
     */
    private JSONObject specs;

    /**
     * 系统通用
     */
    private Boolean isSys;

    /**
     * 图表展示
     */
    private Boolean isChart;

    /**
     * 实时监测
     */
    private Boolean isMonitor;

    /**
     * 历史存储
     */
    private Boolean isHistory;

    /**
     * 只读数据
     */
    private Boolean isReadonly;

    /**
     * 设备分享
     */
    private Boolean isSharePerm;

    /**
     * 排序
     */
    private Integer modelOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 从机id
     */
    private String tempSlaveId;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 控制公式
     */
    private String reverseFormula;

    /**
     * 寄存器地址值
     */
    private Integer regAddr;

    /**
     * 位定义选项
     */
    private String bitOption;

    /**
     * 解析类型 1.数值 2.选项
     */
    private Integer valueType;

    /**
     * 计算参数
     */
    private Boolean isParams;

    /**
     * 读取寄存器数量
     */
    private Integer quantity;

    /**
     * modbus功能码
     */
    private String code;

    /**
     * 旧的标识符
     */
    private String oldIdentifier;

    /**
     * 旧的从机id
     */
    private String oldTempSlaveId;

    /**
     * modbus解析类型
     */
    private String parseType;


}
