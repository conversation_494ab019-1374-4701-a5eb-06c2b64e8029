package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.ProtocolBo;
import org.dromara.pdkj.domain.vo.ProtocolVo;

import java.util.Collection;
import java.util.List;

/**
 * 协议管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface IProtocolService {

    /**
     * 查询协议管理
     *
     * @param id 主键
     * @return 协议管理
     */
    ProtocolVo queryById(Long id);

    /**
     * 分页查询协议管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 协议管理分页列表
     */
    TableDataInfo<ProtocolVo> queryPageList(ProtocolBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的协议管理列表
     *
     * @param bo 查询条件
     * @return 协议管理列表
     */
    List<ProtocolVo> queryList(ProtocolBo bo);

    /**
     * 新增协议管理
     *
     * @param bo 协议管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ProtocolBo bo);

    /**
     * 修改协议管理
     *
     * @param bo 协议管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ProtocolBo bo);

    /**
     * 校验并批量删除协议管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
