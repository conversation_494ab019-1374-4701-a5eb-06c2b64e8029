package org.dromara.mqtt.handler.mqtt.impl;


import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessage;
import io.netty.handler.codec.mqtt.MqttMessageType;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.util.AttributeUtils;
import org.dromara.mqtt.annotation.Process;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.manager.ResponseManager;
import org.dromara.mqtt.utils.MqttMessageUtils;

/**
 * 客户端Ping消息应答
 *
 * <AUTHOR>
 */
@Slf4j
@Process(type = MqttMessageType.PINGREQ)
public class MqttPingreq implements MqttHandler {

    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        /*获取客户端id*/
        String clientId = AttributeUtils.getClientId(ctx.channel());
        try {
            // log.debug("=>客户端:{},心跳信息", clientId);
            /*更新客户端ping时间*/
            ClientManager.updatePing(clientId);
            /*响应设备的ping消息*/
            MqttMessage pingResp = MqttMessageUtils.buildPingResp();
            ResponseManager.sendMessage(pingResp, clientId, true);
        } catch (Exception e) {
            log.error("=>客户端:{},ping异常:{}", clientId, e);
        }
    }
}
