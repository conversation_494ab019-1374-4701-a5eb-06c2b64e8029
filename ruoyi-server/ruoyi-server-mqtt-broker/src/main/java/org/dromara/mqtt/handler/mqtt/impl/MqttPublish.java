package org.dromara.mqtt.handler.mqtt.impl;


import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.*;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.enums.ServerType;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.mq.redischannel.producer.MessageProducer;
import org.dromara.mq.service.IDeviceReportMessageService;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.manager.ResponseManager;
import org.dromara.mqtt.manager.RetainMsgManager;
import org.dromara.mqtt.model.ClientMessage;
import org.dromara.mqtt.service.IMessageStore;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 客户端消息推送处理类
 *
 * <AUTHOR>
 */
@Slf4j
@org.dromara.mqtt.annotation.Process(type = MqttMessageType.PUBLISH)
public class MqttPublish implements MqttHandler {

    @Autowired
    private IMessageStore messageStore;
    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private IDeviceReportMessageService deviceReportMessageService;

//    @Resource
//    private RuleProcess ruleProcess;

    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        MqttPublishMessage publishMessage = (MqttPublishMessage) message;
        /*获取客户端id*/
        String clientId = AttributeUtils.getClientId(ctx.channel());
        String topicName = publishMessage.variableHeader().topicName();
        log.debug("=>***客户端[{}],主题[{}],推送消息[{}]", clientId, topicName,
            ByteBufUtil.hexDump(publishMessage.content()));
        // 以get结尾是模拟客户端数据,只转发消息
        if (topicName.endsWith(PdkjConstant.MQTT.PROPERTY_GET_SIMULATE)) {
            sendTestToMQ(publishMessage);
        } else {
            /*获取客户端session*/
            Session session = AttributeUtils.getSession(ctx.channel());
            /*推送保留信息*/
            pubRetain(publishMessage);
            /*响应客户端消息到达Broker*/
            callBack(session, publishMessage, clientId);
            /*推送到订阅的客户端*/
            sendMessageToClients(publishMessage);
            /*推送到MQ处理*/
            sendToMQ(publishMessage);
            /*累计接收消息数*/
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_RECEIVE_TOTAL, -1L);
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_RECEIVE_TODAY, 60 * 60 * 24);
        }
    }

    /**
     * 消息推送
     *
     * @param message 推送消息
     */
    @SneakyThrows
    public void sendToMQ(MqttPublishMessage message) {
        /*获取topic*/
        String topicName = message.variableHeader().topicName();
        byte[] source = ByteBufUtil.getBytes(message.content());
        /*只处理上报数据*/
        if (!topicName.endsWith(PdkjConstant.MQTT.UP_TOPIC_SUFFIX)) {
            return;
        }
        DeviceReportBo reportBo = DeviceReportBo.builder()
            .deviceCode(topicsUtils.parseSerialNumber(topicName))
            .topicName(topicName)
            .packetId((long) message.variableHeader().packetId())
            .platformDate(DateUtils.getNowDate())
            .data(ByteBufUtil.getBytes(message.content()))
            .serverType(ServerType.MQTT)
            .build();
        if (topicName.endsWith(PdkjConstant.TOPIC.MSG_REPLY) ||
            topicName.endsWith(PdkjConstant.TOPIC.SUB_UPGRADE_REPLY) ||
            topicName.endsWith(PdkjConstant.TOPIC.UPGRADE_REPLY)) {
            /*设备应答服务器回调数据*/
            reportBo.setReportType(2);
        } else {
            /*设备上报数据*/
            reportBo.setReportType(1);
        }
        // 规则引擎脚本处理,完成后返回结果
//        MsgContext context = ruleProcess.processRuleScript(reportBo.getSerialNumber(),1, topicName, new String(source));
//        if (!Objects.isNull(context) && StringUtils.isNotEmpty(context.getPayload())
//                && StringUtils.isNotEmpty(context.getTopic())) {
//            reportBo.setTopicName(context.getTopic());
//            reportBo.setData(context.getPayload().getBytes(StandardCharsets.UTF_8));
//        }
        if (topicName.contains("property")) {
            deviceReportMessageService.parseReportMsg(reportBo);
        }

    }

    /**
     * 发送模拟数据进行处理
     *
     * @param message
     */
    public void sendTestToMQ(MqttPublishMessage message) {
        /*获取topic*/
        String topicName = message.variableHeader().topicName();
        DeviceReportBo reportBo = DeviceReportBo.builder()
            .deviceCode(topicsUtils.parseSerialNumber(topicName))
            .topicName(topicName)
            .packetId((long) message.variableHeader().packetId())
            .platformDate(DateUtils.getNowDate())
            .data(ByteBufUtil.getBytes(message.content()))
            .build();
        MessageProducer.sendOtherMsg(reportBo);
    }


    /**
     * 推送消息到订阅客户端
     *
     * @param message 消息
     */
    public void sendMessageToClients(MqttPublishMessage message) {
        ClientManager.pubTopic(message);
    }


    /**
     * 应答客户端，消息到达Broker
     *
     * @param session 客户端
     * @param message 消息
     */
    private void callBack(Session session, MqttPublishMessage message, String clientId) {
        /*获取消息等级*/
        MqttQoS mqttQoS = message.fixedHeader().qosLevel();
        int packetId = message.variableHeader().packetId();
        MqttFixedHeader header;
        switch (mqttQoS.value()) {
            /*0,1消息等级，直接回复*/
            case 0:
            case 1:
                header = new MqttFixedHeader(MqttMessageType.PUBACK, false, mqttQoS, false, 0);
                break;
            case 2:
                // 处理Qos2的消息确认
                if (!messageStore.outRelContains(packetId)) {
                    messageStore.saveRelInMsg(packetId);
                }
                header = new MqttFixedHeader(MqttMessageType.PUBREC, false, MqttQoS.AT_MOST_ONCE, false, 0);
                break;
            default:
                header = null;
        }
        /*处理消息等级*/
        handleMqttQos(packetId, mqttQoS, true, clientId);
        /*响应客户端*/
        MqttMessageIdVariableHeader variableHeader = null;
        if (packetId > 0) {
            variableHeader = MqttMessageIdVariableHeader.from(packetId);
        }
        MqttPubAckMessage ackMessage = new MqttPubAckMessage(header, variableHeader);
        if (mqttQoS.value() >= 1) {
            ResponseManager.responseMessage(session, ackMessage, true);
        }
        /*更新客户端ping时间*/
        ClientManager.updatePing(session.getClientId());

    }

    /**
     * Qos不同消息处理
     */
    private void handleMqttQos(int packetId, MqttQoS qoS, boolean clearSession, String clientId) {
        if (qoS == MqttQoS.AT_LEAST_ONCE || qoS == MqttQoS.EXACTLY_ONCE) {
            ClientMessage clientMessage = ClientMessage.of(clientId, qoS, null, false);
            messageStore.savePubMsg(packetId, clientMessage);
        }
    }


    /**
     * 推送保留信息
     */
    @SneakyThrows
    private void pubRetain(MqttPublishMessage message) {
        RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_RETAIN_TOTAL, -1L);
        /*根据message.fixedHeader().isRetain() 判断是否有保留信息*/
        RetainMsgManager.pushMessage(message);
    }


}
