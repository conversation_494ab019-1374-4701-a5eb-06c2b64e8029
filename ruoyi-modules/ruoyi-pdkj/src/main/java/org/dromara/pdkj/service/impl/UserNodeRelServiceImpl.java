package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.UserNodeRel;
import org.dromara.pdkj.domain.bo.UserNodeRelBo;
import org.dromara.pdkj.domain.vo.UserNodeRelVo;
import org.dromara.pdkj.mapper.UserNodeRelMapper;
import org.dromara.pdkj.service.IUserNodeRelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 住户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class UserNodeRelServiceImpl implements IUserNodeRelService {

    private final UserNodeRelMapper baseMapper;

    /**
     * 查询住户管理
     *
     * @param id 主键
     * @return 住户管理
     */
    @Override
    public UserNodeRelVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询住户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 住户管理分页列表
     */
    @Override
    public TableDataInfo<UserNodeRelVo> queryPageList(UserNodeRelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserNodeRel> lqw = buildQueryWrapper(bo);
        Page<UserNodeRelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的住户管理列表
     *
     * @param bo 查询条件
     * @return 住户管理列表
     */
    @Override
    public List<UserNodeRelVo> queryList(UserNodeRelBo bo) {
        LambdaQueryWrapper<UserNodeRel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<UserNodeRel> buildQueryWrapper(UserNodeRelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserNodeRel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(UserNodeRel::getId);
        lqw.eq(bo.getNodeId() != null, UserNodeRel::getNodeId, bo.getNodeId());
        lqw.eq(bo.getUserId() != null, UserNodeRel::getUserId, bo.getUserId());
        lqw.eq(bo.getAppAuthStatus() != null, UserNodeRel::getAppAuthStatus, bo.getAppAuthStatus());
        lqw.eq(bo.getAppAuthStart() != null, UserNodeRel::getAppAuthStart, bo.getAppAuthStart());
        lqw.eq(bo.getAppAuthEnd() != null, UserNodeRel::getAppAuthEnd, bo.getAppAuthEnd());
        lqw.eq(bo.getFaceAuthStatus() != null, UserNodeRel::getFaceAuthStatus, bo.getFaceAuthStatus());
        lqw.eq(bo.getFaceAuthStart() != null, UserNodeRel::getFaceAuthStart, bo.getFaceAuthStart());
        lqw.eq(bo.getFaceAuthEnd() != null, UserNodeRel::getFaceAuthEnd, bo.getFaceAuthEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getNodePath()), UserNodeRel::getNodePath, bo.getNodePath());
        lqw.eq(bo.getEstateId() != null, UserNodeRel::getEstateId, bo.getEstateId());
        return lqw;
    }

    /**
     * 新增住户管理
     *
     * @param bo 住户管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(UserNodeRelBo bo) {
        UserNodeRel add = MapstructUtils.convert(bo, UserNodeRel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改住户管理
     *
     * @param bo 住户管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(UserNodeRelBo bo) {
        UserNodeRel update = MapstructUtils.convert(bo, UserNodeRel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserNodeRel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除住户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
