package org.dromara.mqtt.manager;


import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessageType;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.service.ISessionStore;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.mq.bo.DeviceStatusBo;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.mqtt.utils.MqttMessageUtils;
import org.dromara.pdkj.service.impl.IDeviceCache;
import org.springframework.util.StringUtils;

/**
 * 会话管理类
 *
 * <AUTHOR>
 * @Date 2022/9/12 20:22
 */
@Slf4j
public class SessionManger {


    private static ISessionStore sessionStore = SpringUtils.getBean(ISessionStore.class);
    private static MqttRemoteManager remoteManager = SpringUtils.getBean(MqttRemoteManager.class);
    private static IDeviceCache deviceCache = SpringUtils.getBean(IDeviceCache.class);

    /**
     * mqtt新客户连接
     *
     * @param clientId 客户端id
     * @param session  客户端
     * @param notice
     */
    public static void buildSession(String clientId, Session session, boolean notice) {
        log.info("=>新客户端连接，clientId={}，id={}", clientId, session.getHandlerContext().channel().id().asLongText());
        if (!StringUtils.hasLength(clientId) || handleContext(session)) {
            log.error("=>客户端id为空或者session未注册!");
            return;
        }

        sessionStore.storeSession(clientId, session);
        //contextMap.put(session.getHandlerContext(), session);
        /*更新客户端在平台的最新响应时间*/
        ClientManager.updatePing(clientId);
        /*发送MQ，设备上线*/
        DeviceStatusBo statusBo = MqttMessageUtils.buildStatusMsg(session.getHandlerContext(), session.getClientId(), DeviceStatus.ONLINE, session.getIp());
//        if (!statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WM_PREFIX) &&
//            !statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WS_PREFIX) &&
//            !statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.FAST_PHONE)) {
//            deviceCache.updateDeviceStatusCache(statusBo);
//            remoteManager.pushDeviceStatus(-1L, statusBo.getDeviceCode(), statusBo.getStatus());
//        }

        try {
            deviceCache.updateDeviceStatusCache(statusBo, notice);
        } catch (Exception ignored) {

        }
        remoteManager.pushDeviceStatus(-1L, statusBo.getDeviceCode(), statusBo.getStatus());
    }

    /**
     * 根据客户端id移除客户端
     *
     * @param clientId 客户端id
     * @param notice
     */
    public static void removeClient(String clientId, boolean notice) {
        log.info("=>移除客户端,clientId={}, notice={}", clientId, notice);
        try {
            if (StringUtils.isEmpty(clientId) || !sessionStore.containsKey(clientId) || clientId.endsWith(PdkjConstant.SERVER.WS_PREFIX) ||
                clientId.endsWith(PdkjConstant.SERVER.FAST_PHONE)) {
                return;
            }
            Session session = sessionStore.getSession(clientId);
            if (handleContext(session)) {
                log.error("移除客户端失败,客户端未注册!");
                return;
            }
            log.warn("=>移除客户端,clientId={}, id={}", session.getClientId(), session.getHandlerContext().channel().id().asLongText());
            //关闭通道
            session.getHandlerContext().close();
            //移除client
            sessionStore.cleanSession(clientId);
            session.setMqttMessageType(MqttMessageType.DISCONNECT);
            //发送至MQ,设备下线
            if (notice) {
                DeviceStatusBo statusBo = MqttMessageUtils.buildStatusMsg(session.getHandlerContext(), session.getClientId(), DeviceStatus.OFFLINE, session.getIp());
                if (!statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WM_PREFIX) &&
                    !statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WS_PREFIX)) {
                    try {
                        deviceCache.updateDeviceStatusCache(statusBo, true);
                    } catch (Exception ignored) {

                    }
                    remoteManager.pushDeviceStatus(-1L, statusBo.getDeviceCode(), statusBo.getStatus());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("移除客户端失败,message=" + e.getMessage());
        }
    }

    /**
     * 根据客户通道移除客户端
     *
     * @param ctx 上下文通道
     */
    public static void removeContextByContext(ChannelHandlerContext ctx) {
        try {
            /*获取*/
            Session session = AttributeUtils.getSession(ctx.channel());
            if (handleContext(session)) {
                log.error("=>客户端通道不存在!移除失败");
                return;
            }
            Session storeSession = sessionStore.getSession(session.getClientId());
            // 说明有新的连接了，后面就不要移除了
            if (storeSession != null && storeSession.getHandlerContext().channel().isActive()) {
                return;
            }

            log.warn("=>移除客户端,clientId={}, id={}", session.getClientId(), ctx.channel().id().asLongText());
            sessionStore.cleanSession(session.getClientId());
            session.setMqttMessageType(MqttMessageType.DISCONNECT);
            //发送至MQ,设备下线
            DeviceStatusBo statusBo = MqttMessageUtils.buildStatusMsg(session.getHandlerContext(), session.getClientId(), DeviceStatus.OFFLINE, session.getIp());
//            if (!statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WM_PREFIX) &&
//                !statusBo.getDeviceCode().startsWith(PdkjConstant.SERVER.WS_PREFIX)) {
//                  deviceCache.updateDeviceStatusCache(statusBo);
//                  remoteManager.pushDeviceStatus(-1L, statusBo.getDeviceCode(), statusBo.getStatus());
//            }
            try {
                deviceCache.updateDeviceStatusCache(statusBo, true);
            } catch (Exception ignored) {

            }
            remoteManager.pushDeviceStatus(-1L, statusBo.getDeviceCode(), statusBo.getStatus());
        } catch (Exception e) {
            log.error("=>移除客户端失败={}", e.getMessage());
        }
    }

    /**
     * 根据客户通道获取客户端ID
     *
     * @param ctx 上下文通道
     * @return 客户端ID
     */
    public static Session getSessionByContext(ChannelHandlerContext ctx) {

        Session session = AttributeUtils.getSession(ctx.channel());
        if (handleContext(session)) {
            log.error("=>客户端通道不存在!获取失败");
            return null;
        }
        return session;
    }

    /**
     * ping判定时间超时
     *
     * @param clientId 客户id
     */
    public static void pingTimeout(String clientId) {
        try {
            removeClient(clientId, true);
        } catch (Exception e) {
            throw new ServiceException("移除超时客户端失败");
        }
    }

    /**
     * 根据clientId获取客户通道
     *
     * @param clientId 客户端id
     * @return session
     */
    public static Session getSession(String clientId) {
        Session session = sessionStore.getSession(clientId);
        // 验证session的有效性
        if (session != null && !isSessionValid(session)) {
            log.warn("检测到客户端[{}]的session无效，清理session", clientId);
            sessionStore.cleanSession(clientId);
            ClientManager.remove(clientId);
            return null;
        }
        return session;
    }

    /**
     * 验证Session是否有效
     *
     * @param session 客户端session
     * @return 是否有效
     */
    private static boolean isSessionValid(Session session) {
        if (session == null || session.getHandlerContext() == null) {
            return false;
        }

        try {
            // 检查Channel是否仍然活跃
            return session.getHandlerContext().channel().isActive();
        } catch (Exception e) {
            log.debug("检查session有效性时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 清理所有无效的Session
     * 定时任务调用此方法
     */
    public static void cleanInvalidSessions() {
        try {
            java.util.concurrent.ConcurrentHashMap<String, Session> sessionMap = sessionStore.getSessionMap();
            java.util.List<String> invalidClientIds = new java.util.ArrayList<>();

            sessionMap.forEach((clientId, session) -> {
                if (!isSessionValid(session)) {
                    invalidClientIds.add(clientId);
                }
            });

            if (!invalidClientIds.isEmpty()) {
                log.info("发现{}个无效session，开始清理: {}", invalidClientIds.size(), invalidClientIds);
                for (String clientId : invalidClientIds) {
                    try {
                        sessionStore.cleanSession(clientId);
                        ClientManager.remove(clientId);
                        log.debug("已清理无效session: {}", clientId);
                    } catch (Exception e) {
                        log.error("清理session[{}]时发生异常: {}", clientId, e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("清理无效session时发生异常", e);
        }
    }

    /**
     * 校验Session已经注册通道
     *
     * @param session 客户端
     * @return 结果
     */
    private static boolean handleContext(Session session) {
        if (null == session || null == session.getHandlerContext()) {
            return true;
        }
        return false;
    }


}
