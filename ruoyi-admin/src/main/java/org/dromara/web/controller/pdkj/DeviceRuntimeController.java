package org.dromara.web.controller.pdkj;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.enums.UserType;
import org.dromara.common.core.mq.InvokeReqDto;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.mq.service.IFunctionInvoke;
import org.dromara.pdkj.domain.vo.DeviceLogVo;
import org.dromara.pdkj.service.IDeviceRuntimeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备运行状态
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/device/runtime")
public class DeviceRuntimeController extends BaseController {

    private final IDeviceRuntimeService deviceRuntimeService;
    private final IFunctionInvoke functionInvoke;

    /**
     * 生成设备编号
     */
    @GetMapping("/runState")
    public R<List<DeviceLogVo>> runState(String deviceCode, Integer type, Long productId, Integer slaveId) {
        ThingsModelType modelType = ThingsModelType.getType(type);
        List<DeviceLogVo> list = deviceRuntimeService.runState(deviceCode, modelType, productId, slaveId);
        return R.ok(list);
    }

    /**
     * 生成设备编号
     */
    @GetMapping("/service/invoke")
    public R<String> serviceInvoke(@Validated InvokeReqDto bo) {
        bo.setValue(new JSONObject(bo.getRemoteCommand()));
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserType.SYS_USER);

        String messageId = functionInvoke.invokeNoReply(bo);
        return R.ok("操作成功", messageId);
    }


}
