<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-vue-plus</artifactId>
        <version>5.3.0</version>
    </parent>

    <artifactId>ruoyi-server</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>ruoyi-server-mqtt-broker</module>
        <module>ruoyi-server-base-server</module>
        <module>ruoyi-server-core</module>
        <module>ruoyi-server-boot-strap</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>ruoyi-common-core</artifactId>
        </dependency>

    </dependencies>

</project>
