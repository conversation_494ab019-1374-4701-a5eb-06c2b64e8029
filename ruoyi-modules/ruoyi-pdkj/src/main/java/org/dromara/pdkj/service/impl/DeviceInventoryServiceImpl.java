package org.dromara.pdkj.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.DeviceInventory;
import org.dromara.pdkj.domain.Product;
import org.dromara.pdkj.domain.bo.DeviceInventoryAddBo;
import org.dromara.pdkj.domain.bo.DeviceInventoryBo;
import org.dromara.pdkj.domain.vo.DeviceInventoryVo;
import org.dromara.pdkj.mapper.DeviceInventoryMapper;
import org.dromara.pdkj.mapper.ProductMapper;
import org.dromara.pdkj.service.IDeviceInventoryService;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 设备出厂Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RequiredArgsConstructor
@Service
public class DeviceInventoryServiceImpl implements IDeviceInventoryService {

    private final DeviceInventoryMapper baseMapper;
    private final ProductMapper productMapper;
    private final IDeviceService deviceService;

    /**
     * 查询设备出厂
     *
     * @param id 主键
     * @return 设备出厂
     */
    @Override
    public DeviceInventoryVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备出厂列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备出厂分页列表
     */
    @Override
    public TableDataInfo<DeviceInventoryVo> queryPageList(DeviceInventoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceInventory> lqw = buildQueryWrapper(bo);
        Page<DeviceInventoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备出厂列表
     *
     * @param bo 查询条件
     * @return 设备出厂列表
     */
    @Override
    public List<DeviceInventoryVo> queryList(DeviceInventoryBo bo) {
        LambdaQueryWrapper<DeviceInventory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceInventory> buildQueryWrapper(DeviceInventoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceInventory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceInventory::getId);
        lqw.eq(bo.getProductId() != null, DeviceInventory::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceSecret()), DeviceInventory::getDeviceSecret, bo.getDeviceSecret());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), DeviceInventory::getDeviceCode, bo.getDeviceCode());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), DeviceInventory::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceAlias()), DeviceInventory::getDeviceAlias, bo.getDeviceAlias());
        lqw.eq(bo.getDeviceStatus() != null, DeviceInventory::getDeviceStatus, bo.getDeviceStatus());
        lqw.eq(bo.getLastActivateTime() != null, DeviceInventory::getLastActivateTime, bo.getLastActivateTime());
        lqw.eq(bo.getLastDeactivateTime() != null, DeviceInventory::getLastDeactivateTime, bo.getLastDeactivateTime());
        lqw.eq(bo.getBindStatus() != null, DeviceInventory::getBindStatus, bo.getBindStatus());
        return lqw;
    }

    /**
     * 新增设备出厂
     *
     * @param bo 设备出厂
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceInventoryAddBo bo) {

        List<DeviceInventory> list = new ArrayList<>();
        for (int i = 0; i < bo.getDeviceNum(); i++) {
            DeviceInventory inventory = new DeviceInventory();
            inventory.setDeviceCode("D" + RandomUtil.randomString(19).toUpperCase());
            inventory.setDeviceSecret(RandomUtil.randomString(32));
            inventory.setRemark(bo.getRemark());
            inventory.setProductId(bo.getProductId());
            inventory.setDeviceStatus(0);
            list.add(inventory);
        }
        baseMapper.insertBatch(list);
        return true;
    }

    /**
     * 修改设备出厂
     *
     * @param bo 设备出厂
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceInventoryBo bo) {
        DeviceInventory update = MapstructUtils.convert(bo, DeviceInventory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceInventory entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备出厂信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String registerByMac(String productSymbol, String deviceMac, String deviceName) {

        Product product = productMapper.selectOne(new LambdaQueryWrapper<Product>().eq(Product::getProductSymbol, productSymbol).last("limit 1"));
        if (product == null) {
            throw new ServiceException("产品[" + productSymbol + "]不存在");
        }
        DeviceInventory device = baseMapper.selectOne(new LambdaQueryWrapper<DeviceInventory>().eq(DeviceInventory::getDeviceMac, deviceMac).last("limit 1"));
        if (device != null) {
            return device.getDeviceCode();
        }

        DeviceInventory inventory = baseMapper.selectOne(new LambdaQueryWrapper<DeviceInventory>().isNull(DeviceInventory::getDeviceMac).last("limit 1"));
        if (inventory == null) {
            throw new ServiceException("没有可用的设备");
        }
        inventory.setDeviceName(deviceName);
        inventory.setDeviceMac(deviceMac);
        inventory.setProductId(product.getId());
        inventory.setDeviceStatus(1);
        inventory.setLastActivateTime(new Date());
        baseMapper.updateById(inventory);

        deviceService.cacheDeviceStatus(product.getId(), inventory.getDeviceCode());
        return inventory.getDeviceCode();
    }

}
