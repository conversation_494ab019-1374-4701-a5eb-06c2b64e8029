package org.dromara.mq.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.ThingsModelType;
import org.dromara.common.core.mq.InvokeReqDto;
import org.dromara.common.core.mq.bo.MQSendMessageBo;
import org.dromara.common.core.utils.SnowflakeIdWorker;
import org.dromara.common.core.utils.bean.BeanUtils;
import org.dromara.mq.model.ReportDataBo;
import org.dromara.mq.service.IDataHandler;
import org.dromara.mq.service.IFunctionInvoke;
import org.dromara.mq.service.IMqttMessagePublish;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2022/12/5 11:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FunctionInvokeImpl implements IFunctionInvoke {

    private final IMqttMessagePublish mqttMessagePublish;
    private final IDataHandler dataHandler;
    private SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(2);


    /**
     * 服务调用,设备不响应
     *
     * @param reqDto 服务下发对象
     * @return 消息id messageId
     */
    @Override
    public String invokeNoReply(InvokeReqDto reqDto) {
        log.debug("=>下发指令请求：[{}]", reqDto);
        MQSendMessageBo bo = new MQSendMessageBo();
        BeanUtils.copyBeanProp(bo, reqDto);
        long id = snowflakeIdWorker.nextId();
        String messageId = id + "";
        bo.setMessageId(messageId);
        bo.setType(ThingsModelType.getType(reqDto.getType()));
        ReportDataBo reportDataBo = mqttMessagePublish.funcSend(bo);
        if (reportDataBo != null) {
            dataHandler.reportData(reportDataBo);
        }
        return messageId;
    }
}
