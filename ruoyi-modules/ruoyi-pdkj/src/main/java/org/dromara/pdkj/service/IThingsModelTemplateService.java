package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.ThingsModelTemplateBo;
import org.dromara.pdkj.domain.vo.ThingsModelTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 通用物模型Service接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface IThingsModelTemplateService {

    /**
     * 查询通用物模型
     *
     * @param id 主键
     * @return 通用物模型
     */
    ThingsModelTemplateVo queryById(Long id);

    /**
     * 分页查询通用物模型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 通用物模型分页列表
     */
    TableDataInfo<ThingsModelTemplateVo> queryPageList(ThingsModelTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的通用物模型列表
     *
     * @param bo 查询条件
     * @return 通用物模型列表
     */
    List<ThingsModelTemplateVo> queryList(ThingsModelTemplateBo bo);

    /**
     * 新增通用物模型
     *
     * @param bo 通用物模型
     * @return 是否新增成功
     */
    Boolean insertByBo(ThingsModelTemplateBo bo);

    /**
     * 修改通用物模型
     *
     * @param bo 通用物模型
     * @return 是否修改成功
     */
    Boolean updateByBo(ThingsModelTemplateBo bo);

    /**
     * 校验并批量删除通用物模型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
