package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.pdkj.domain.Node;

/**
 * 楼栋管理业务对象 tb_node
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@AutoMapper(target = Node.class, reverseConvertGenerate = false)
public class NodeEditBo {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空")
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String nodeName;

}
