package org.dromara.pdkj.service;

import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.common.core.thingsModel.ThingsModelValueItem;
import org.dromara.common.core.thingsModel.ThingsModelValuesInput;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.bo.DeviceBo;
import org.dromara.pdkj.domain.vo.DeviceVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IDeviceService {

    /**
     * 查询设备管理
     *
     * @param id 主键
     * @return 设备管理
     */
    DeviceVo queryById(Long id);

    /**
     * 分页查询设备管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备管理分页列表
     */
    TableDataInfo<DeviceVo> queryPageList(DeviceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备管理列表
     *
     * @param bo 查询条件
     * @return 设备管理列表
     */
    List<DeviceVo> queryList(DeviceBo bo);

    /**
     * 新增设备管理
     *
     * @param bo 设备管理
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceBo bo);

    /**
     * 修改设备管理
     *
     * @param bo 设备管理
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceBo bo);

    /**
     * 校验并批量删除设备管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    String generationDeviceCode(Integer deviceType);

    Long countByProductId(Long productId);

    DeviceVo selectDeviceRunningStatusByDeviceId(Long id, Integer slaveId);
    DeviceVo selectDeviceRunningStatusByDeviceCode(String deviceCode, Integer slaveId);

    DeviceVo selectDeviceByDeviceCode(String deviceCode);

    int reportDevice(Device device, DeviceVo deviceVo);

    List<ThingsModelSimpleItem> reportDeviceThingsModelValue(ThingsModelValuesInput input, int type, boolean shadow);

    int updateDeviceStatusAndLocation(DeviceVo device, String ip, boolean insertEvent);

    List<ThingsModelValueItem> cacheDeviceStatus(Long productId, String deviceCode);

    String registerByMac(String productSymbol, String deviceMac, String deviceName);

    Boolean updateById(Device update);
}
