package org.dromara.common.core.thingsModel;

import lombok.Data;

/**
 * 数据定义
 *
 * <AUTHOR>
 */
@Data
public class Specs {

    private String id;
    private String name;
    private Boolean isMonitor;
    private Integer slaveId;
    private Boolean isChart;
    private Boolean isHistory;
    private String datatype;
    /**
     * 计算公式
     */
    private String formula;


    @Data
    static class Datatype {

        private String unit;
        private Integer min;
        private Integer max;
        private Integer step;
        private String type;
        private String trueText;
        private String falseText;
        private EnumList enumList;
    }

    @Data
    static class EnumList {
        private String text;
        private String value;
    }
}
