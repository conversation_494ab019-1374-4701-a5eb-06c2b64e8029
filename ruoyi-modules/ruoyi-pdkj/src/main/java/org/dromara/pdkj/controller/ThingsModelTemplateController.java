package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.ThingsModelTemplateBo;
import org.dromara.pdkj.domain.vo.ThingsModelTemplateVo;
import org.dromara.pdkj.service.IThingsModelTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通用物模型
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/thingsModelTemplate")
public class ThingsModelTemplateController extends BaseController {

    private final IThingsModelTemplateService thingsModelTemplateService;

    /**
     * 查询通用物模型列表
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<ThingsModelTemplateVo> list(ThingsModelTemplateBo bo, PageQuery pageQuery) {
        return thingsModelTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出通用物模型列表
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:export")
    @Log(title = "通用物模型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ThingsModelTemplateBo bo, HttpServletResponse response) {
        List<ThingsModelTemplateVo> list = thingsModelTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "通用物模型", ThingsModelTemplateVo.class, response);
    }

    /**
     * 获取通用物模型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:query")
    @GetMapping("/{id}")
    public R<ThingsModelTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(thingsModelTemplateService.queryById(id));
    }

    /**
     * 新增通用物模型
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:add")
    @Log(title = "通用物模型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ThingsModelTemplateBo bo) {
        return toAjax(thingsModelTemplateService.insertByBo(bo));
    }

    /**
     * 修改通用物模型
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:edit")
    @Log(title = "通用物模型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ThingsModelTemplateBo bo) {
        return toAjax(thingsModelTemplateService.updateByBo(bo));
    }

    /**
     * 删除通用物模型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:thingsModelTemplate:remove")
    @Log(title = "通用物模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(thingsModelTemplateService.deleteWithValidByIds(List.of(ids), true));
    }
}
