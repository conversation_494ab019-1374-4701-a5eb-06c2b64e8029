package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.EstateBo;
import org.dromara.pdkj.domain.vo.EstateVo;
import org.dromara.pdkj.service.IEstateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小区信息
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/estate")
public class EstateController extends BaseController {

    private final IEstateService estateService;

    /**
     * 查询小区信息列表
     */
    @SaCheckPermission("pdkj:estate:list")
    @GetMapping("/list")
    public TableDataInfo<EstateVo> list(EstateBo bo, PageQuery pageQuery) {
        return estateService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询小区信息列表
     */
    @SaCheckPermission("pdkj:estate:list")
    @GetMapping("/list/all")
    public R<List<EstateVo>> listAll(EstateBo bo) {
        return R.ok(estateService.queryList(bo));
    }

    /**
     * 导出小区信息列表
     */
    @SaCheckPermission("pdkj:estate:export")
    @Log(title = "小区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EstateBo bo, HttpServletResponse response) {
        List<EstateVo> list = estateService.queryList(bo);
        ExcelUtil.exportExcel(list, "小区信息", EstateVo.class, response);
    }

    /**
     * 获取小区信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:estate:query")
    @GetMapping("/{id}")
    public R<EstateVo> getInfo(@NotNull(message = "主键不能为空")
                               @PathVariable Integer id) {
        return R.ok(estateService.queryById(id));
    }

    /**
     * 新增小区信息
     */
    @SaCheckPermission("pdkj:estate:add")
    @Log(title = "小区信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EstateBo bo) {
        return toAjax(estateService.insertByBo(bo));
    }

    /**
     * 修改小区信息
     */
    @SaCheckPermission("pdkj:estate:edit")
    @Log(title = "小区信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EstateBo bo) {
        return toAjax(estateService.updateByBo(bo));
    }

    /**
     * 删除小区信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:estate:remove")
    @Log(title = "小区信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Integer[] ids) {
        return toAjax(estateService.deleteWithValidByIds(List.of(ids), true));
    }
}
