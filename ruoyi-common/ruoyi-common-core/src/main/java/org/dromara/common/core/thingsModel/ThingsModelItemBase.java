package org.dromara.common.core.thingsModel;

import lombok.Data;

@Data
public class ThingsModelItemBase {
    /**
     * 物模型唯一标识符
     */
    private String id;
    /**
     * 物模型名称
     */
    private String name;
    /**
     * 物模型值
     */
    private Object value;
    /**
     * 是否首页显示（0-否，1-是）
     */
    private Boolean isChart;
    /**
     * 是否实时监测（0-否，1-是）
     */
    private Boolean isMonitor;
    /**
     * 类型 1=属性，2=功能，3=事件
     */
    private Integer type;
    /**
     * 数据类型
     */
    private String dataType;
    /**
     * 影子值
     */
    private Object shadow;

}
