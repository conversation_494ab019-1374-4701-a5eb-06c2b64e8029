package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 协议管理对象 tb_protocol
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot.protocol")
public class Protocol extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 协议编码
     */
    private String protocolCode;

    /**
     * 协议名称
     */
    private String protocolName;

    /**
     * 协议jar包,js包，c程序上传地址
     */
    private String protocolFileUrl;

    /**
     * 协议类型 0:未知 1:jar，2.js,3.c
     */
    private Integer protocolType;

    /**
     * 协议文件摘要(文件的md5)
     */
    private String jarSign;

    /**
     * 0:草稿 1:启用 2:停用
     */
    private Long protocolStatus;

    /**
     * 0:正常 1:删除
     */
    @TableLogic
    private Integer delFlag;


}
