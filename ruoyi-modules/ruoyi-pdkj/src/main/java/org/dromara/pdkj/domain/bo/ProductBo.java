package org.dromara.pdkj.domain.bo;

import cn.hutool.json.JSONObject;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.Product;

/**
 * 产品管理业务对象 tb_product
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Product.class, reverseConvertGenerate = false)
public class ProductBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productName;

    /**
     * 产品分类
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 物模型
     */
    private JSONObject thingsModelsJson;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 联网方式
     */
    private Long networkMethod;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 产品支持的传输协议
     */
    private String transport;

    /**
     * 备注
     */
    private String remark;

    /**
     * mqtt账号
     */
    private String mqttAccount;

    /**
     * mqtt密码
     */
    private String mqttPassword;

    /**
     * 产品秘钥
     */
    private String mqttSecret;

    /**
     * 认证方式
     */
    private Long vertificateMethod;

    /**
     * 协议编号
     */
    private String protocolCode;

    private Boolean isAuthorize;

    private String productSymbol;
}
