package org.dromara.common.core.thingsModel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.core.utils.DateUtils;

import java.util.Date;

/**
 * 物模型值的项
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class ThingsModelSimpleItem {
    /**
     * 物模型唯一标识符
     */
    private String id;

    /**
     * 物模型值
     */
    private Object value;

    /**
     * 更新时间
     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ts;

    private Integer slaveId;

    /**
     * 备注
     **/
    private String remark;

    private String timestamp;

    private boolean isBit = false;

    public ThingsModelSimpleItem(String id, String value, String remark) {
        this.id = id;
        this.value = value;
        this.remark = remark;
    }

    public ThingsModelSimpleItem(String id, String value, Integer slaveId, String remark) {
        this.id = id;
        this.value = value;
        this.slaveId = slaveId;
        this.remark = remark;
    }


    public void setTs(Date ts) {
        this.ts = ts == null ? DateUtils.getNowDate() : ts;
    }
}
