package org.dromara.common.mybatis.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.dromara.common.core.utils.StringUtils;

import java.lang.reflect.Field;

@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class HutoolJsonObjectTypeHandler extends AbstractJsonTypeHandler<Object> {

    public HutoolJsonObjectTypeHandler(Class<?> type) {
        super(type);
    }

    public HutoolJsonObjectTypeHandler(Class<?> type, Field field) {
        super(type, field);
    }

    @Override
    public Object parse(String json) {
        if (StringUtils.isBlank(json) || "null".equals(json)) {
            return new JSONObject();
        }
        return JSONUtil.toBean(json, JSONObject.class);
    }

    @Override
    public String toJson(Object obj) {
        return JSONUtil.toJsonStr(obj);
    }
}
