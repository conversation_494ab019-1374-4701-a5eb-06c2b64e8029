package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.UserDeviceRel;
import org.dromara.pdkj.domain.bo.UserDeviceRelBo;
import org.dromara.pdkj.domain.vo.UserDeviceRelVo;
import org.dromara.pdkj.mapper.UserDeviceRelMapper;
import org.dromara.pdkj.service.IUserDeviceRelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RequiredArgsConstructor
@Service
public class UserDeviceRelServiceImpl implements IUserDeviceRelService {

    private final UserDeviceRelMapper baseMapper;

    /**
     * 查询用户设备
     *
     * @param id 主键
     * @return 用户设备
     */
    @Override
    public UserDeviceRelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户设备分页列表
     */
    @Override
    public TableDataInfo<UserDeviceRelVo> queryPageList(UserDeviceRelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserDeviceRel> lqw = buildQueryWrapper(bo);
        Page<UserDeviceRelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户设备列表
     *
     * @param bo 查询条件
     * @return 用户设备列表
     */
    @Override
    public List<UserDeviceRelVo> queryList(UserDeviceRelBo bo) {
        LambdaQueryWrapper<UserDeviceRel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<UserDeviceRel> buildQueryWrapper(UserDeviceRelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserDeviceRel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(UserDeviceRel::getId);
        lqw.eq(bo.getDeviceId() != null, UserDeviceRel::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getUserId() != null, UserDeviceRel::getUserId, bo.getUserId());
        lqw.eq(bo.getRoleType() != null, UserDeviceRel::getRoleType, bo.getRoleType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), UserDeviceRel::getDeviceCode, bo.getDeviceCode());
        return lqw;
    }

    /**
     * 新增用户设备
     *
     * @param bo 用户设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(UserDeviceRelBo bo) {
        UserDeviceRel add = MapstructUtils.convert(bo, UserDeviceRel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户设备
     *
     * @param bo 用户设备
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(UserDeviceRelBo bo) {
        UserDeviceRel update = MapstructUtils.convert(bo, UserDeviceRel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserDeviceRel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
