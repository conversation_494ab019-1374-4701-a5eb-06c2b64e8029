package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.UserNodeRelBo;
import org.dromara.pdkj.domain.vo.UserNodeRelVo;

import java.util.Collection;
import java.util.List;

/**
 * 住户管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IUserNodeRelService {

    /**
     * 查询住户管理
     *
     * @param id 主键
     * @return 住户管理
     */
    UserNodeRelVo queryById(String id);

    /**
     * 分页查询住户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 住户管理分页列表
     */
    TableDataInfo<UserNodeRelVo> queryPageList(UserNodeRelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的住户管理列表
     *
     * @param bo 查询条件
     * @return 住户管理列表
     */
    List<UserNodeRelVo> queryList(UserNodeRelBo bo);

    /**
     * 新增住户管理
     *
     * @param bo 住户管理
     * @return 是否新增成功
     */
    Boolean insertByBo(UserNodeRelBo bo);

    /**
     * 修改住户管理
     *
     * @param bo 住户管理
     * @return 是否修改成功
     */
    Boolean updateByBo(UserNodeRelBo bo);

    /**
     * 校验并批量删除住户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
