package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.ProductAuthorize;

import java.io.Serial;
import java.io.Serializable;



/**
 * 产品授权码视图对象 tb_product_authorize
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductAuthorize.class)
public class ProductAuthorizeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 授权码ID
     */
    @ExcelProperty(value = "授权码ID")
    private Long id;

    /**
     * 授权码
     */
    @ExcelProperty(value = "授权码")
    private String authorizeCode;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_product_authorize_status")
    private Integer status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
