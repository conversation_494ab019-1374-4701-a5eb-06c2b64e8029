package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.DeviceLogBo;
import org.dromara.pdkj.domain.vo.DeviceLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备日志Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IDeviceLogService {

    /**
     * 查询设备日志
     *
     * @param id 主键
     * @return 设备日志
     */
    DeviceLogVo queryById(Long id);

    /**
     * 分页查询设备日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备日志分页列表
     */
    TableDataInfo<DeviceLogVo> queryPageList(DeviceLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备日志列表
     *
     * @param bo 查询条件
     * @return 设备日志列表
     */
    List<DeviceLogVo> queryList(DeviceLogBo bo);

    /**
     * 新增设备日志
     *
     * @param bo 设备日志
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceLogBo bo);

    /**
     * 修改设备日志
     *
     * @param bo 设备日志
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceLogBo bo);

    /**
     * 校验并批量删除设备日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
