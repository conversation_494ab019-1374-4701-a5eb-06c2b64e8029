package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 事件日志对象 tb_event_log
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("log.event_log")
public class EventLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备事件日志ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 标识符
     */
    private String identity;

    /**
     * 物模型名称
     */
    private String modelName;

    /**
     * 类型
     */
    private Integer logType;

    /**
     * 日志值
     */
    private String logValue;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 监测数据
     */
    private Boolean isMonitor;

    /**
     * 模式
     */
    private Integer mode;

    /**
     * 备注
     */
    private String remark;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Long delFlag;

    /**
     * 图片
     */
    private String picUrl;
}
