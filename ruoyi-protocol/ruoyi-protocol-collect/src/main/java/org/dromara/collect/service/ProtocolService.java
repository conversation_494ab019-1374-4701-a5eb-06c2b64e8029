package org.dromara.collect.service;


import org.dromara.collect.base.IDeviceProtocol;
import org.dromara.common.core.annotation.SysProtocol;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ProtocolService implements ApplicationListener<ContextRefreshedEvent> {

    private static final Map<String, IDeviceProtocol> protocolMap = new HashMap<>();

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {

        ApplicationContext ac = event.getApplicationContext();
        Map<String, Object> beansWithAnnotation = ac.getBeansWithAnnotation(SysProtocol.class);
        beansWithAnnotation.forEach((k, v) -> {
            if (v instanceof IDeviceProtocol protocol) {
                protocolMap.put(protocol.getClass().getAnnotation(SysProtocol.class).protocolCode(), protocol);
            }
        });
    }


    public IDeviceProtocol getProtocol(String protocolCode) {
        IDeviceProtocol protocol = protocolMap.get(protocolCode);
        if (protocol == null) {
            throw new ServiceException("协议[" + protocolCode + "]不存在");
        }
        return protocol;
    }
}
