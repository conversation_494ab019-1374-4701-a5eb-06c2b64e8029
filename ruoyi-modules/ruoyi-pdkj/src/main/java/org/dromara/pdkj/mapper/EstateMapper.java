package org.dromara.pdkj.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.Estate;
import org.dromara.pdkj.domain.vo.EstateVo;

import java.util.Collection;
import java.util.List;

/**
 * 小区信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface EstateMapper extends BaseMapperPlus<Estate, EstateVo> {
    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default List<EstateVo> selectVoList(Wrapper<Estate> wrapper) {
        return BaseMapperPlus.super.selectVoList(wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default <P extends IPage<EstateVo>> P selectVoPage(IPage<Estate> page, Wrapper<Estate> wrapper) {
        return BaseMapperPlus.super.selectVoPage(page, wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }
}
