package org.dromara.collect.base.impl;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.collect.base.IDeviceProtocol;
import org.dromara.common.core.annotation.SysProtocol;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.mq.DeviceReport;
import org.dromara.common.core.mq.message.DeviceData;
import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.common.core.thingsModel.ThingsModelValuesInput;
import org.dromara.common.core.thingsModel.ValueItem;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/10 16:55
 */
@Slf4j
@Component
@SysProtocol(name = "JSONArray解析协议", protocolCode = PdkjConstant.PROTOCOL.JsonArray, description = "系统内置JSONArray解析协议")
public class JsonDeviceProtocolService implements IDeviceProtocol {


    @Override
    public DeviceReport decode(DeviceData deviceData, String clientId) {
        try {
            DeviceReport reportMessage = new DeviceReport();
            // bytep[] 转String
            String data = new String(deviceData.getData(), StandardCharsets.UTF_8);

            List<ThingsModelSimpleItem> values = JsonUtils.parseArray(data, ThingsModelSimpleItem.class);
            //上报数据时间
            for (ThingsModelSimpleItem value : values) {
                value.setTs(DateUtils.getNowDate());
            }
            ThingsModelValuesInput valuesInput = new ThingsModelValuesInput();
            valuesInput.setThingsModelSimpleItem(values);
            reportMessage.setValuesInput(valuesInput);
            reportMessage.setClientId(clientId);
            reportMessage.setDeviceCode(clientId);
            return reportMessage;
        } catch (Exception e) {
            throw new ServiceException("数据解析异常" + e.getMessage());
        }
    }


    @Override
    public byte[] encode(DeviceData message, String clientId) {
        try {
            JSONObject body = JSONUtil.parseObj(message.getDownMessage().getBody());
            ValueItem valueItem = new ValueItem();
            for (Map.Entry<String, Object> entry : body) {
                valueItem.setId(entry.getKey());
                valueItem.setValue(entry.getValue().toString());
                valueItem.setRemark("");
            }
            String msg = "[" + JsonUtils.toJsonString(valueItem) + "]";
            return msg.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("=>指令编码异常,device={},data={}", message.getDeviceCode(),
                message.getDownMessage().getBody());
            return null;
        }
    }

    @Override
    public byte[] checkDevice(String clientId) {
        return null;
    }
}
