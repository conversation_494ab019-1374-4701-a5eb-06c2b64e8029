package org.dromara.pdkj.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.Product;
import org.dromara.pdkj.domain.vo.ProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface ProductMapper extends BaseMapperPlus<Product, ProductVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default List<ProductVo> selectVoList(Wrapper<Product> wrapper) {
        return BaseMapperPlus.super.selectVoList(wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default <P extends IPage<ProductVo>> P selectVoPage(IPage<Product> page, Wrapper<Product> wrapper) {
        return BaseMapperPlus.super.selectVoPage(page, wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    int updateById(@Param(Constants.ENTITY) Product entity);

}
