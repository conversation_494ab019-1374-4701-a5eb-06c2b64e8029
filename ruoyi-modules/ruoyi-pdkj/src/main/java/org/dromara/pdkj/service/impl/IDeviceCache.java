package org.dromara.pdkj.service.impl;


import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.mq.bo.DeviceStatusBo;
import org.dromara.pdkj.domain.vo.DeviceVo;

import java.util.List;

/**
 * 设备缓存
 *
 * <AUTHOR>
 */
public interface IDeviceCache {

    /**
     * 更新设备状态
     *
     * @param dto         dto
     * @param insertEvent
     */
    DeviceVo updateDeviceStatusCache(DeviceStatusBo dto, boolean insertEvent);

    /**
     * 批量更新redis缓存设备状态
     *
     * @param serialNumbers 设备列表
     * @param status        状态
     */
    void updateBatchDeviceStatusCache(List<String> serialNumbers, DeviceStatus status);


}
