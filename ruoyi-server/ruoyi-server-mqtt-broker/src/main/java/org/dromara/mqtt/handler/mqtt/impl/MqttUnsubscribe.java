package org.dromara.mqtt.handler.mqtt.impl;

import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.MqttMessage;
import io.netty.handler.codec.mqtt.MqttMessageType;
import io.netty.handler.codec.mqtt.MqttUnsubAckMessage;
import io.netty.handler.codec.mqtt.MqttUnsubscribeMessage;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.mqtt.annotation.Process;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.manager.ResponseManager;
import org.dromara.mqtt.utils.MqttMessageUtils;

import java.util.List;


@Slf4j
@Process(type = MqttMessageType.UNSUBSCRIBE)
public class MqttUnsubscribe implements MqttHandler {

    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        MqttUnsubscribeMessage unsubscribeMessage = (MqttUnsubscribeMessage) message;
        List<String> topics = unsubscribeMessage.payload().topics();
        log.debug("=>收到取消订阅请求,topics[{}]", topics);
        Session session = AttributeUtils.getSession(ctx.channel());
        topics.forEach(topic -> {
            ClientManager.unsubscribe(topic, session);
        });
        MqttUnsubAckMessage unsubAckMessage = MqttMessageUtils.buildUnsubAckMessage(unsubscribeMessage);
        ResponseManager.responseMessage(session, unsubAckMessage, true);
        /*更新客户端平台时间*/
        ClientManager.updatePing(session.getClientId());
    }
}
