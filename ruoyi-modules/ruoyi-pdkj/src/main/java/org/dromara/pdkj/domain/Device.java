package org.dromara.pdkj.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.HutoolJsonArrayTypeHandler;

import java.io.Serial;
import java.util.Date;

/**
 * 设备管理对象 tb_device
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "device.device", autoResultMap = true)
public class Device extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 删除标志
     */
    @TableLogic
    private Integer delFlag;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备密钥
     */
    private String deviceSecret;

    /**
     * 楼栋ID
     */
    private Long nodeId;

    /**
     * 楼栋路径
     */
    private String nodePath;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 子设备网关编号
     */
    private String gwDevCode;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 信号强度（
     * 信号极好4格[-55— 0]，
     * 信号好3格[-70— -55]，
     * 信号一般2格[-85— -70]，
     * 信号差1格[-100— -85]）
     */
    private Integer rssi;

    /**
     * 是否启用设备影子(0=禁用，1=启用)
     */
    private Boolean isShadow;

    /**
     * 定位方式
     */
    private Integer locationWay;

    /**
     * 物模型值
     */
    @TableField(typeHandler = HutoolJsonArrayTypeHandler.class)
    private JSONArray thingsModelValue;

    /**
     * 设备所在地址
     */
    private String networkAddress;

    /**
     * 设备入网IP
     */
    private String networkIp;

    /**
     * 设备经度
     */
    private Double longitude;

    /**
     * 设备纬度
     */
    private Double latitude;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 设备摘要
     */
    private String summary;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否是模拟设备
     */
    private Boolean isSimulate;

    /**
     * 从机id
     */
    private Integer slaveId;

    /**
     * 小区ID
     */
    private Long estateId;

    /**
     * 设备Mac地址
     */
    private String deviceMac;

    /**
     * 设备签名
     */
    private String sign;

    /**
     * 产品标识
     */
    private String productSymbol;
}
