package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.NodeAddBo;
import org.dromara.pdkj.domain.bo.NodeBo;
import org.dromara.pdkj.domain.bo.NodeEditBo;
import org.dromara.pdkj.domain.vo.NodeVo;
import org.dromara.pdkj.service.INodeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 楼栋管理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/node")
public class NodeController extends BaseController {

    private final INodeService nodeService;

    /**
     * 查询楼栋管理列表
     */
    @SaCheckPermission("pdkj:node:list")
    @GetMapping("/list")
    public R<List<NodeVo>> list(NodeBo bo) {
        List<NodeVo> list = nodeService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出楼栋管理列表
     */
    @SaCheckPermission("pdkj:node:export")
    @Log(title = "楼栋管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(NodeBo bo, HttpServletResponse response) {
        List<NodeVo> list = nodeService.queryList(bo);
        ExcelUtil.exportExcel(list, "楼栋管理", NodeVo.class, response);
    }

    /**
     * 获取楼栋管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:node:query")
    @GetMapping("/{id}")
    public R<NodeVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(nodeService.queryById(id));
    }

    /**
     * 新增楼栋管理
     */
    @SaCheckPermission("pdkj:node:add")
    @Log(title = "楼栋管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody NodeAddBo bo) {
        return toAjax(nodeService.insertByBo(bo));
    }

    /**
     * 修改楼栋管理
     */
    @SaCheckPermission("pdkj:node:edit")
    @Log(title = "楼栋管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody NodeEditBo bo) {
        return toAjax(nodeService.updateByBo(MapstructUtils.convert(bo, NodeBo.class)));
    }

    /**
     * 删除楼栋管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:node:remove")
    @Log(title = "楼栋管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(nodeService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 查询楼栋管理树形结构数据
     */
    @SaCheckPermission("pdkj:node:list")
    @GetMapping("/tree")
    public R<List<Tree<Long>>> tree(@Validated(QueryGroup.class) NodeBo bo) {
        List<Tree<Long>> list = nodeService.queryTree(bo);
        return R.ok(list);
    }

}
