package org.dromara.collect.command;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public interface LancenLockCommand {
    // 获取P2P请求
    int GET_P2P_REQ = 40;
    // 获取P2P回复
    int GET_P2P_RES = 41;

    // 心跳请求
    int HEARTBEAT_REQ = 46;
    // 心跳回复
    int HEARTBEAT_RES = 47;

    //获取配置
    int GET_CONFIG_REQ = 48;
    //获取配置回复
    int GET_CONFIG_RES = 49;

    // 解锁日志请求
    int UNLOCK_LOG_REQ = 62;
    // 解锁日志回复
    int UNLOCK_LOG_RES = 63;

    // 上传图片请求
    int UPLOAD_PIC_REQ = 65;

    // 开锁
    int UNLOCK = 100;

    // 请求版本号上报
    int FIRMWARE_VERSION_REQ = 110;
    // 请求版本号回复
    int FIRMWARE_VERSION_RES = 111;

    // 获取锁电量
    int LOCK_POWER_REQ = 114;
    // 锁电量回复
    int LOCK_POWER_RES = 115;

    // 开锁准备请求
    int UNLOCK_READY_REQ = 1001;

    // 上传配置
    int UPLOAD_CONFIGURATION_REQ = 10000;


    @Getter
    enum PushType {

        PUSH_TYPE_PIR(0),

        PUSH_TYPE_DOORBELL(1),

        PUSH_TYPE_RESERVED1(2),

        PUSH_TYPE_RESERVED2(3),

        PUSH_TYPE_LOW_POWER(4),

        PUSH_TYPE_LOCK_EVENT(5),

        PUSH_TYPE_LOCK_EVENT_WITH_CATCH(6),


        PUSH_TYPE_TOT_STANDARD(7),

        PUSH_TYPE_LOW_TEMP_ALARM(8),

        PUSH_TYPE_HIGHT_TEMP_ALARM(9),

        PUSH_TYPE_NOISE_ALARM(10),

        PUSH_TYPE_SOS(11),

        PUSH_TYPE_STAY_ALARM(12),

        PUSH_TYPE_CALL_INDOOR(13);

        private final int type;

        PushType(int type) {
            this.type = type;
        }

    }

    @Getter
    enum OpenLockType {
        FINGERPRINT("00", "指纹开锁"),
        PASSWORD("01", "密码开锁"),
        CARD("02", "刷卡开锁"),
        CONTROL("03", "远程开锁"),
        KEY("04", "钥匙开锁"),
        EYES("05", "瞳孔开锁"),
        PALM_VEIN("06", "掌静脉开锁"),
        FINGER_VEIN("07", "指静脉开锁"),
        FACE("08", "人脸开锁"),
        REMOTE("09", "APP开锁"),
        IN_DOOR("0A", "门内开锁"),
        COMBINE("0B", "组合开锁");

        private final String type;
        private final String desc;

        OpenLockType(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static OpenLockType byType(String type) {
            for (OpenLockType value : values()) {
                if (value.type.equals(type)) {
                    return value;
                }
            }
            return OpenLockType.IN_DOOR;
        }
    }
}
