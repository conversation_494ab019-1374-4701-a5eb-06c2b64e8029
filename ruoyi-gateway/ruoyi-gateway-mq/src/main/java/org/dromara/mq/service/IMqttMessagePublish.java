package org.dromara.mq.service;


import org.dromara.common.core.enums.TopicType;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.common.core.mq.bo.MQSendMessageBo;
import org.dromara.common.core.mq.message.DeviceDownMessage;
import org.dromara.common.core.mq.message.InstructionsMessage;
import org.dromara.common.core.mq.ota.OtaUpgradeBo;
import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.mq.model.ReportDataBo;

import java.util.List;

public interface IMqttMessagePublish {

    /**
     * 下发数据编码
     */
    InstructionsMessage buildMessage(DeviceDownMessage downMessage, TopicType type);

    /**
     * 服务(指令)下发
     *
     * @return
     */
    public ReportDataBo funcSend(MQSendMessageBo bo);

    /**
     * OTA升级下发
     */
    public void upGradeOTA(OtaUpgradeBo bo);

    public void sendFunctionMessage(DeviceReportBo bo);


    /**
     * 1.发布设备状态
     */
    public void publishStatus(Long productId, String deviceNum, int deviceStatus, Boolean isShadow, int rssi);


    /**
     * 2.发布设备信息
     */
    public void publishInfo(Long productId, String deviceNum);


    /**
     * 3.发布时钟同步信息
     *
     * @param bo 数据模型
     */
    public void publishNtp(ReportDataBo bo);


    /**
     * 4.发布属性
     * delay 延时，秒为单位
     */
    public void publishProperty(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay);


    /**
     * 5.发布功能
     * delay 延时，秒为单位
     */
    public void publishFunction(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay);


    /**
     * 设备数据同步
     *
     * @param deviceNumber 设备编号
     * @return 设备
     */
//    public Device deviceSynchronization(String deviceNumber);


}
