package org.dromara.push.service.impl;

import cn.hutool.json.JSONUtil;
import com.aliyun.sdk.service.push20160801.models.PushRequest;
import com.aliyun.sdk.service.push20160801.models.PushResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.push.config.PushConfig;
import org.dromara.push.enums.PushDeviceType;
import org.dromara.push.enums.PushTarget;
import org.dromara.push.enums.PushType;
import org.dromara.push.service.IPushService;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PushServiceImpl implements IPushService {

    private final PushConfig pushConfig;

    @Override
    public void push2ios(PushType pushType, PushTarget pushTarget, String targetValue, String title, String body) {
        PushRequest pushRequest = PushRequest.builder()
            .appKey(pushConfig.getIos().getAppKey())
            .iOSApnsEnv(pushConfig.getIos().getEnv())
            .deviceType(PushDeviceType.iOS.name())
            .pushType(pushType.name())
            .target(pushTarget.name())
            .targetValue(targetValue)
            .title(title)
            .body(body)
            .build();

        CompletableFuture<PushResponse> response = pushConfig.asyncClient(pushConfig.provider()).push(pushRequest);
        try {
            PushResponse resp = response.get();
            log.info("ios推送结果--> req=[{}], res=[{}]", JSONUtil.parseObj(pushRequest), JSONUtil.parseObj(resp));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
