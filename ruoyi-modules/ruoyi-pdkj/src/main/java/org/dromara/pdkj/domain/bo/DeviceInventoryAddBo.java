package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.pdkj.domain.DeviceInventory;
import org.hibernate.validator.constraints.Range;

/**
 * 设备出厂业务对象 device_inventory
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@AutoMapper(target = DeviceInventory.class, reverseConvertGenerate = false)
public class DeviceInventoryAddBo {
    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    /**
     * 设备状态
     */
    @NotNull(message = "设备状态数量不能为空")
    @Range(min = 1, max = 1000, message = "设备状态数量范围只能1-1000")
    private Integer deviceNum;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;

}
