package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.ProtocolBo;
import org.dromara.pdkj.domain.vo.ProtocolVo;
import org.dromara.pdkj.service.IProtocolService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 协议管理
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/protocol")
public class ProtocolController extends BaseController {

    private final IProtocolService protocolService;

    /**
     * 查询协议管理列表
     */
    @SaCheckPermission("pdkj:protocol:list")
    @GetMapping("/list")
    public TableDataInfo<ProtocolVo> list(ProtocolBo bo, PageQuery pageQuery) {
        return protocolService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询协议管理列表
     */
    @GetMapping("/list/all")
    public R<List<ProtocolVo>> listAll(ProtocolBo bo) {
        return R.ok(protocolService.queryList(bo));
    }

    /**
     * 导出协议管理列表
     */
    @SaCheckPermission("pdkj:protocol:export")
    @Log(title = "协议管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProtocolBo bo, HttpServletResponse response) {
        List<ProtocolVo> list = protocolService.queryList(bo);
        ExcelUtil.exportExcel(list, "协议管理", ProtocolVo.class, response);
    }

    /**
     * 获取协议管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:protocol:query")
    @GetMapping("/{id}")
    public R<ProtocolVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(protocolService.queryById(id));
    }

    /**
     * 新增协议管理
     */
    @SaCheckPermission("pdkj:protocol:add")
    @Log(title = "协议管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProtocolBo bo) {
        return toAjax(protocolService.insertByBo(bo));
    }

    /**
     * 修改协议管理
     */
    @SaCheckPermission("pdkj:protocol:edit")
    @Log(title = "协议管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProtocolBo bo) {
        return toAjax(protocolService.updateByBo(bo));
    }

    /**
     * 删除协议管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:protocol:remove")
    @Log(title = "协议管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(protocolService.deleteWithValidByIds(List.of(ids), true));
    }
}
