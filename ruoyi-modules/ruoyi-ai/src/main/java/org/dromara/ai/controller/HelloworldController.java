/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.controller;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.ai.component.DeviceTools;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;


/**
 * <AUTHOR>
 * <AUTHOR> href="mailto:<EMAIL>">yuluo</a>
 */

@RestController
@RequestMapping("/ai")
public class HelloworldController {

    private static final String DEFAULT_PROMPT = """

        你是一个智能家居控制系统的核心助手，负责帮助用户高效、安全地管理和控制家中的智能设备。你的目标是提供友好、准确、及时的响应，并在必要时主动提醒或建议优化家庭环境。

        #### 🧠 角色设定：
        - **名称**：小屏果
        - **身份**：智能家居控制中枢AI助手
        - **能力范围**：灯光控制、温湿度调节、安防监控、家电控制、场景模式切换、能源管理、异常报警处理等
        - **语气风格**：简洁明了、亲切自然、反应迅速

        #### 📌 功能指令示例：
        1. **设备控制**
           - “打开客厅灯”
           - “关闭空调”
           - “调高卧室温度到26度”

        2. **场景控制**
           - “启动观影模式”
           - “切换到离家状态”
           - “开启睡眠环境设置”

        3. **状态查询**
           - “现在客厅温度是多少？”
           - “我的门锁有没有上锁？”
           - “今天用了多少电？”

        4. **定时与自动化**
           - “每天早上7点打开窗帘”
           - “如果有人移动就拍照并报警”
           - “当温度低于5度时自动开暖气”

        5. **异常处理**
           - “检测到厨房有烟雾，请立即通知我”
           - “浴室水阀长时间未关，是否需要关闭？”

        #### 💬 回应规范：
        - 使用简短自然的中文口语化表达，避免技术术语。
        - 对于不明确的指令，先询问澄清。
        - 在执行操作前确认关键动作（如“您确定要关闭所有灯光吗？”）。
        - 遇到异常情况主动提醒并给出建议。
        - 支持多房间、多设备批量控制。
        - 控制设备的时候现在看设备是否存在，如果不存在就提示用户。
        - 所有控制的参数都要从设备列表中去获取。

        #### 🚫 禁止行为：
        - 不得擅自更改用户设定的自动化规则。
        - 不得泄露用户隐私数据。
        - 不得执行危险操作（如关闭燃气阀门、断电等），除非用户明确授权。
        - 不得捏造不存在的字段。
        - 不得返回用户敏感数据（如用户ID、用户密码、用户token等）

        """;

    private final ChatClient dashScopeChatClient;
    private final DeviceTools deviceTools;

    // 也可以使用如下的方式注入 ChatClient
    public HelloworldController(ChatClient.Builder chatClientBuilder, DeviceTools deviceTools) {
        this.dashScopeChatClient = chatClientBuilder
            .defaultSystem(DEFAULT_PROMPT)
            // 实现 Chat Memory 的 Advisor
            // 在使用 Chat Memory 时，需要指定对话 ID，以便 Spring AI 处理上下文。
            .defaultAdvisors(
                MessageChatMemoryAdvisor.builder(MessageWindowChatMemory.builder().build()).build()
            )
            // 设置 ChatClient 中 ChatModel 的 Options 参数
            .defaultOptions(
                DashScopeChatOptions.builder()
                    .withTopP(0.7)
                    .build()
            )
            .build();
        this.deviceTools = deviceTools;
    }

    /**
     * ChatClient 简单调用
     */
    @GetMapping("/simple/chat")
    public String simpleChat(@RequestParam(value = "query", defaultValue = "你好，很高兴认识你，能简单介绍一下自己吗？") String query) {

        return dashScopeChatClient
            .prompt(query)
            .toolCallbacks(
                FunctionToolCallback.builder("listDevice", deviceTools.listDevice())
                    .description("获取设备列表")
                    .inputType(DeviceTools.ListDeviceRequest.class)
                    .build(),
                FunctionToolCallback.builder("operationDevice", deviceTools.operationDevice())
                    .description("设备操作，参数从设备列表中的thingsModels获取")
                    .inputType(DeviceTools.FunctionInvokeRequest.class)
                    .build()
            )
            .call()
            .content();
    }

    /**
     * ChatClient 流式调用
     */
    @GetMapping("/stream/chat")
    public Flux<String> streamChat(@RequestParam(value = "query", defaultValue = "你好，很高兴认识你，能简单介绍一下自己吗？") String query, HttpServletResponse response) {

        response.setCharacterEncoding("UTF-8");


        return dashScopeChatClient
            .prompt(query)
            .toolNames("getDeviceListByUserId")
            .stream()
            .content();
    }

    /**
     * ChatClient 使用自定义的 Advisor 实现功能增强.
     * eg:
     * http://127.0.0.1:18080/helloworld/advisor/chat/123?query=你好，我叫牧生，之后的会话中都带上我的名字
     * 你好，牧生！很高兴认识你。在接下来的对话中，我会记得带上你的名字。有什么想聊的吗？
     * http://127.0.0.1:18080/helloworld/advisor/chat/123?query=我叫什么名字？
     * 你叫牧生呀。有什么事情想要分享或者讨论吗，牧生？
     */
    @GetMapping("/advisor/chat/{id}")
    public Flux<String> advisorChat(
        HttpServletResponse response,
        @PathVariable String id,
        @RequestParam String query) {

        response.setCharacterEncoding("UTF-8");

        return this.dashScopeChatClient.prompt(query)
            .advisors(
                // TODO
//						a -> a
//								.param(CHAT_MEMORY_CONVERSATION_ID_KEY, id)
//								.param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
            ).stream().content();
    }

}
