# 环境变量配置示例文件
# 复制此文件为 .env 并填入实际的配置值

# 数据库配置
DB_URL=*****************************************************************************************************************************************
DB_USERNAME=postgres
DB_PASSWORD=your_database_password

# MQTT配置
MQTT_USERNAME=backend
MQTT_PASSWORD=your_mqtt_password
MQTT_HOST_URL=tcp://localhost:1883

# JWT密钥配置
JWT_SECRET_KEY=your_jwt_secret_key_at_least_32_characters_long

# Token配置
TOKEN_SECRET=your_token_secret_key_at_least_32_characters_long

# P2P配置
P2P_CRC_KEY=your_p2p_crc_key
P2P_KEY=your_p2p_key
P2P_INIT_STRING=your_p2p_init_string

# 阿里云推送配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_IOS_ENV=DEV
ALIYUN_IOS_APP_KEY=your_ios_app_key
ALIYUN_IOS_APP_SECRET=your_ios_app_secret
ALIYUN_ANDROID_APP_KEY=your_android_app_key
ALIYUN_ANDROID_APP_SECRET=your_android_app_secret

# AI配置
AI_DASHSCOPE_API_KEY=your_dashscope_api_key

# 监控配置
MONITOR_USERNAME=admin
MONITOR_PASSWORD=your_monitor_password

# Redis配置（如果需要）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
