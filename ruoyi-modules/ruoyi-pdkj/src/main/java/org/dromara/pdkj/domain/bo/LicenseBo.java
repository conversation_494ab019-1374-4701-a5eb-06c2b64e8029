package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.License;

/**
 * 尚云p2p业务对象 license
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = License.class, reverseConvertGenerate = false)
public class LicenseBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * DID
     */
    @NotBlank(message = "DID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String did;

    /**
     * license
     */
    @NotBlank(message = "license不能为空", groups = { AddGroup.class, EditGroup.class })
    private String license;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 备注
     */
    private String remark;


}
