package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.FunctionLogBo;
import org.dromara.pdkj.domain.vo.FunctionLogVo;
import org.dromara.pdkj.service.IFunctionLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 下发日志
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/functionLog")
public class FunctionLogController extends BaseController {

    private final IFunctionLogService functionLogService;

    /**
     * 查询下发日志列表
     */
    @SaCheckPermission("pdkj:functionLog:list")
    @GetMapping("/list")
    public TableDataInfo<FunctionLogVo> list(FunctionLogBo bo, PageQuery pageQuery) {
        return functionLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出下发日志列表
     */
    @SaCheckPermission("pdkj:functionLog:export")
    @Log(title = "下发日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FunctionLogBo bo, HttpServletResponse response) {
        List<FunctionLogVo> list = functionLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "下发日志", FunctionLogVo.class, response);
    }

    /**
     * 获取下发日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:functionLog:query")
    @GetMapping("/{id}")
    public R<FunctionLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(functionLogService.queryById(id));
    }

    /**
     * 新增下发日志
     */
    @SaCheckPermission("pdkj:functionLog:add")
    @Log(title = "下发日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FunctionLogBo bo) {
        return toAjax(functionLogService.insertByBo(bo));
    }

    /**
     * 修改下发日志
     */
    @SaCheckPermission("pdkj:functionLog:edit")
    @Log(title = "下发日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FunctionLogBo bo) {
        return toAjax(functionLogService.updateByBo(bo));
    }

    /**
     * 删除下发日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:functionLog:remove")
    @Log(title = "下发日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(functionLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
