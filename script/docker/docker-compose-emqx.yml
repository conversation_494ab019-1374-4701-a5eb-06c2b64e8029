version: '3.1'
volumes:
  vol-emqx-ndoe-1:
  vol-emqx-ndoe-2:


services:
  emqx1:
    image: emqx:5.8.5
    container_name: emqx1
    environment:
      - "EMQX_NODE_NAME=<EMAIL>"
      - "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
      - "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>,<EMAIL>]"
    healthcheck:
      test: [ "CMD", "/opt/emqx/bin/emqx", "ctl", "status" ]
      interval: 5s
      timeout: 25s
      retries: 5
    networks:
      emqx-bridge:
        aliases:
          - node1.emqx.io
    ports:
      - 5883:1883
      - 5083:8083
      - 5084:8084
      - 6883:8883
      - 58083:18083
    volumes:
      - vol-emqx-ndoe-2:/opt/emqx/data

  emqx2:
    image: emqx:5.8.5
    container_name: emqx2
    environment:
      - "EMQX_NODE_NAME=<EMAIL>"
      - "EMQX_CLUSTER__DISCOVERY_STRATEGY=static"
      - "EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>,<EMAIL>]"
    healthcheck:
      test: [ "CMD", "/opt/emqx/bin/emqx", "ctl", "status" ]
      interval: 5s
      timeout: 25s
      retries: 5
    networks:
      emqx-bridge:
        aliases:
          - node2.emqx.io
    volumes:
      - vol-emqx-ndoe-2:/opt/emqx/data

networks:
  emqx-bridge:
    driver: bridge
