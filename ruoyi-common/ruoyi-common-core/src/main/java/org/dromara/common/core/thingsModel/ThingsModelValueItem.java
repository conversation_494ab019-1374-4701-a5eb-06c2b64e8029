package org.dromara.common.core.thingsModel;


import lombok.Data;

/**
 * 物模型值的项
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
@Data
public class ThingsModelValueItem {
    /**
     * 物模型唯一标识符
     */
    private String id;

    /**
     * 物模型值
     */
    private Object value;

    /**
     * 影子值
     **/
    private Object shadow;

    /**
     * 是否为监测值
     **/
    private Boolean isMonitor;

    /**
     * 是否为历史存储
     **/
    private Boolean isHistory;

    /**
     * 是否为图表展示
     **/
    private Boolean isChart;

    /**
     * 是否只读数据
     **/
    private Boolean isReadonly;

    /**
     * 物模型名称
     **/
    private String name;

    private Datatype datatype;

    private String ts;

    private String tempSlaveId;

}
