package org.dromara.mqtt.model.lancen;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LancenDeviceConfigRes {
    private int screenonTimeout = 10;
    private Realtime realtime = new Realtime();
    private int cloudStorage = 0;
    private int batDisplayEn = 0;
    private List<Object> iviewsAuth = new ArrayList<>();
    private String time;
    private int targetEv = -1;
    private int unlockLimit = 1;
    private int callScreenOn = 1;
    private int standbyMode = 1;
    private int wetPlay = -1;
}
