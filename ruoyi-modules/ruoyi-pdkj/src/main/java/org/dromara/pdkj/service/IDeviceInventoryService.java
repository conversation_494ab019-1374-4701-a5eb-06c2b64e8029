package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.DeviceInventoryAddBo;
import org.dromara.pdkj.domain.bo.DeviceInventoryBo;
import org.dromara.pdkj.domain.vo.DeviceInventoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 设备出厂Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IDeviceInventoryService {

    /**
     * 查询设备出厂
     *
     * @param id 主键
     * @return 设备出厂
     */
    DeviceInventoryVo queryById(Long id);

    /**
     * 分页查询设备出厂列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备出厂分页列表
     */
    TableDataInfo<DeviceInventoryVo> queryPageList(DeviceInventoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备出厂列表
     *
     * @param bo 查询条件
     * @return 设备出厂列表
     */
    List<DeviceInventoryVo> queryList(DeviceInventoryBo bo);

    /**
     * 新增设备出厂
     *
     * @param bo 设备出厂
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceInventoryAddBo bo);

    /**
     * 修改设备出厂
     *
     * @param bo 设备出厂
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceInventoryBo bo);

    /**
     * 校验并批量删除设备出厂信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    String registerByMac(String productSymbol, String mac, String deviceName);
}
