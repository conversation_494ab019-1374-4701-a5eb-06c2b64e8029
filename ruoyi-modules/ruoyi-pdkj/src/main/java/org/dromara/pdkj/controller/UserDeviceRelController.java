package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.UserDeviceRelBo;
import org.dromara.pdkj.domain.vo.UserDeviceRelVo;
import org.dromara.pdkj.service.IUserDeviceRelService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户设备
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/deviceRel")
public class UserDeviceRelController extends BaseController {

    private final IUserDeviceRelService userDeviceRelService;

    /**
     * 查询用户设备列表
     */
    @SaCheckPermission("pdkj:deviceRel:list")
    @GetMapping("/list")
    public TableDataInfo<UserDeviceRelVo> list(UserDeviceRelBo bo, PageQuery pageQuery) {
        return userDeviceRelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户设备列表
     */
    @SaCheckPermission("pdkj:deviceRel:export")
    @Log(title = "用户设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserDeviceRelBo bo, HttpServletResponse response) {
        List<UserDeviceRelVo> list = userDeviceRelService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户设备", UserDeviceRelVo.class, response);
    }

    /**
     * 获取用户设备详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:deviceRel:query")
    @GetMapping("/{id}")
    public R<UserDeviceRelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(userDeviceRelService.queryById(id));
    }

    /**
     * 新增用户设备
     */
    @SaCheckPermission("pdkj:deviceRel:add")
    @Log(title = "用户设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UserDeviceRelBo bo) {
        return toAjax(userDeviceRelService.insertByBo(bo));
    }

    /**
     * 修改用户设备
     */
    @SaCheckPermission("pdkj:deviceRel:edit")
    @Log(title = "用户设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserDeviceRelBo bo) {
        return toAjax(userDeviceRelService.updateByBo(bo));
    }

    /**
     * 删除用户设备
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:deviceRel:remove")
    @Log(title = "用户设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(userDeviceRelService.deleteWithValidByIds(List.of(ids), true));
    }
}
