package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.EventLog;

/**
 * 事件日志业务对象 tb_event_log
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EventLog.class, reverseConvertGenerate = false)
public class EventLogBo extends BaseEntity {

    /**
     * 设备事件日志ID
     */
    @NotNull(message = "设备事件日志ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 标识符
     */
    @NotBlank(message = "标识符不能为空", groups = { AddGroup.class, EditGroup.class })
    private String identity;

    /**
     * 物模型名称
     */
    private String modelName;

    /**
     * 类型
     */
    @NotNull(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer logType;

    /**
     * 日志值
     */
    @NotBlank(message = "日志值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String logValue;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 监测数据
     */
    private Boolean isMonitor;

    /**
     * 模式
     */
    private Integer mode;

    /**
     * 备注
     */
    private String remark;


}
