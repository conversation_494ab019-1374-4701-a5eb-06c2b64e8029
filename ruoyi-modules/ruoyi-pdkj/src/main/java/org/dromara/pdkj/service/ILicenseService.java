package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.LicenseBo;
import org.dromara.pdkj.domain.vo.LicenseVo;

import java.util.Collection;
import java.util.List;

/**
 * 尚云p2pService接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ILicenseService {

    /**
     * 查询尚云p2p
     *
     * @param id 主键
     * @return 尚云p2p
     */
    LicenseVo queryById(Long id);

    /**
     * 分页查询尚云p2p列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 尚云p2p分页列表
     */
    TableDataInfo<LicenseVo> queryPageList(LicenseBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的尚云p2p列表
     *
     * @param bo 查询条件
     * @return 尚云p2p列表
     */
    List<LicenseVo> queryList(LicenseBo bo);

    /**
     * 新增尚云p2p
     *
     * @param bo 尚云p2p
     * @return 是否新增成功
     */
    Boolean insertByBo(LicenseBo bo);

    /**
     * 修改尚云p2p
     *
     * @param bo 尚云p2p
     * @return 是否修改成功
     */
    Boolean updateByBo(LicenseBo bo);

    /**
     * 校验并批量删除尚云p2p信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
