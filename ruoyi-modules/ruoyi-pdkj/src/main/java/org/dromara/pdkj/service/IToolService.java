package org.dromara.pdkj.service;


import org.dromara.pdkj.domain.bo.MqttAuthenticationModel;
import org.dromara.pdkj.domain.bo.ProductAuthenticateModel;
import org.dromara.pdkj.domain.bo.RegisterUserInput;
import org.dromara.pdkj.domain.vo.RegisterUserOutput;
import org.springframework.http.ResponseEntity;

/**
 *
 * <AUTHOR>
 * @date 2021-12-16
 */
public interface IToolService
{
    /**
     * 注册
     */
    public String register(RegisterUserInput registerBody);

    /**
     * 注册
     */
    public RegisterUserOutput registerNoCaptcha(RegisterUserInput registerBody);

    /**
     * 生成随机数字和字母
     */
    public String getStringRandom(int length);

    public  String generateRandomHex(int length);

    /**
     * 设备简单认证
     */
    public ResponseEntity simpleMqttAuthentication(MqttAuthenticationModel mqttModel, ProductAuthenticateModel productModel);

    /**
     * 设备加密认证
     *
     * @return
     */
    public ResponseEntity encryptAuthentication(MqttAuthenticationModel mqttModel, ProductAuthenticateModel productModel)throws Exception;


    /**
     * 整合设备认证接口
     */
    public ResponseEntity clientAuth(String clientid,String username,String password) throws Exception;
    public ResponseEntity clientAuthv5(String clientid,String username,String password) throws Exception;

    /**
     * 返回认证信息
     */
    public ResponseEntity returnUnauthorized(MqttAuthenticationModel mqttModel, String message);
}
