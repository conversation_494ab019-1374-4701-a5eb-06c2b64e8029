package org.dromara.pdkj.mapper;

import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.ThingsModelTemplate;
import org.dromara.pdkj.domain.vo.ThingsModelTemplateVo;

import java.util.Collection;

/**
 * 通用物模型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface ThingsModelTemplateMapper extends BaseMapperPlus<ThingsModelTemplate, ThingsModelTemplateVo> {

    //
//    @Override
//    @DataPermission({
//        @DataColumn(key = "deptName", value = "create_dept")
//    })
//    default List<ThingsModelTemplateVo> selectVoList(Wrapper<ThingsModelTemplate> wrapper) {
//        return BaseMapperPlus.super.selectVoList(wrapper);
//    }
//
//    @Override
//    @DataPermission({
//        @DataColumn(key = "deptName", value = "create_dept")
//    })
//    default <P extends IPage<ThingsModelTemplateVo>> P selectVoPage(IPage<ThingsModelTemplate> page, Wrapper<ThingsModelTemplate> wrapper) {
//        return BaseMapperPlus.super.selectVoPage(page, wrapper);
//    }
//
    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }

//    @Override
//    @DataPermission({
//        @DataColumn(key = "deptName", value = "create_dept")
//    })
//    int updateById(@Param(Constants.ENTITY) ThingsModelTemplate entity);
}
