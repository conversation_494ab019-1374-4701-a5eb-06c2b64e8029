package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.FunctionLogBo;
import org.dromara.pdkj.domain.vo.FunctionLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 下发日志Service接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface IFunctionLogService {

    /**
     * 查询下发日志
     *
     * @param id 主键
     * @return 下发日志
     */
    FunctionLogVo queryById(Long id);

    /**
     * 分页查询下发日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 下发日志分页列表
     */
    TableDataInfo<FunctionLogVo> queryPageList(FunctionLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的下发日志列表
     *
     * @param bo 查询条件
     * @return 下发日志列表
     */
    List<FunctionLogVo> queryList(FunctionLogBo bo);

    /**
     * 新增下发日志
     *
     * @param bo 下发日志
     * @return 是否新增成功
     */
    Boolean insertByBo(FunctionLogBo bo);

    /**
     * 修改下发日志
     *
     * @param bo 下发日志
     * @return 是否修改成功
     */
    Boolean updateByBo(FunctionLogBo bo);

    /**
     * 校验并批量删除下发日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
