package org.dromara.pdkj.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.HutoolJsonObjectTypeHandler;

import java.io.Serial;

/**
 * 物模型对象 tb_things_model
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "iot.things_model", autoResultMap = true)
public class ThingsModel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物模型ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 物模型名称
     */
    private String modelName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 标识符
     */
    private String identifier;

    /**
     * 模型类别
     */
    private Integer type;

    /**
     * 数据类型
     */
    private String datatype;

    /**
     * 数据定义
     */
    @TableField(typeHandler = HutoolJsonObjectTypeHandler.class)
    private JSONObject specs;

    /**
     * 是否图表展示
     */
    private Boolean isChart;

    /**
     * 是否实时监测
     */
    private Boolean isMonitor;

    /**
     * 是否历史存储
     */
    private Boolean isHistory;

    /**
     * 是否只读数据
     */
    private Boolean isReadonly;

    /**
     * 设备分享权限
     */
    private Boolean isSharePerm;

    /**
     * 排序，值越大，排序越靠前
     */
    private Integer modelOrder;

    /**
     * 删除标志
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 从机id
     */
    private String tempSlaveId;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 控制公式
     */
    private String reverseFormula;

    /**
     * 寄存器地址值
     */
    private Long regAddr;

    /**
     * 位定义选项
     */
    private String bitOption;

    /**
     * 解析类型
     */
    private Integer valueType;

    /**
     * 是计算参数
     */
    private Boolean isParams;

    /**
     * 读取寄存器数量
     */
    private Integer quantity;

    /**
     * modbus功能码
     */
    private String code;

    /**
     * modbus解析类型
     */
    private String parseType;


}
