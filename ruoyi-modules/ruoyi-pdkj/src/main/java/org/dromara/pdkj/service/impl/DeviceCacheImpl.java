package org.dromara.pdkj.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.DeviceStatus;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.mq.bo.DeviceStatusBo;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.redis.utils.RedisKeyBuilder;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.pdkj.domain.vo.DeviceVo;
import org.dromara.pdkj.service.IDeviceService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeviceCacheImpl implements IDeviceCache {

    @Value("${server.device.platform.expried:120}")
    private Integer expireTime;


    private final IDeviceService deviceService;


    /**
     * 更新设备状态
     * 如果设备状态保持不变，更新redis设备最新在线时间
     * 如果设备状态更改，更新redis同时，更新MySQL数据库设备状态
     *
     * @param dto         dto
     * @param insertEvent
     */
    @Override
    public DeviceVo updateDeviceStatusCache(DeviceStatusBo dto, boolean insertEvent) {

        DeviceVo device = deviceService.selectDeviceByDeviceCode(dto.getDeviceCode());
        if (device == null) {
            throw new ServiceException("设备不存在");
        }
        if (dto.getStatus() == DeviceStatus.ONLINE) {
            /*redis设备在线列表*/
            RedisUtils.addCacheScoreSortedSet(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getDeviceCode(), DateUtils.getTimestamp());
            //更新mysql的设备状态为在线，延时500ms解决状态同步问题
            try {
                Thread.sleep(500); // 延迟一秒
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            device.setDeviceStatus(dto.getStatus().getType());
        } else {
            /*在redis设备在线列表移除设备*/
            RedisUtils.delCacheSortedSetItem(RedisKeyBuilder.buildDeviceOnlineListKey(), dto.getDeviceCode());
            //更新一下mysql的设备状态为离线
            device.setDeviceStatus(DeviceStatus.OFFLINE.getType());
        }
        device.setUpdateTime(DateUtils.getNowDate());
        deviceService.updateDeviceStatusAndLocation(device, dto.getIp(), insertEvent);
        return device;
    }


    /**
     * 批量更新redis缓存设备状态
     *
     * @param serialNumbers 设备列表
     * @param status        状态
     */
    @Override
    public void updateBatchDeviceStatusCache(List<String> serialNumbers, DeviceStatus status) {
        if (CollectionUtils.isEmpty(serialNumbers)) {
            return;
        }
        for (String deviceCode : serialNumbers) {
            DeviceStatusBo statusBo = new DeviceStatusBo();
            statusBo.setStatus(status);
            statusBo.setDeviceCode(deviceCode);
            this.updateDeviceStatusCache(statusBo, true);
        }
    }


}
