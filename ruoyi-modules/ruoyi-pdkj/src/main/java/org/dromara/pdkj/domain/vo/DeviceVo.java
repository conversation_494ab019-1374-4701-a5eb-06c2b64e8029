package org.dromara.pdkj.domain.vo;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.thingsModel.*;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.pdkj.domain.Device;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 设备管理视图对象 tb_device
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Device.class)
public class DeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_device_status")
    private Integer deviceStatus;

    /**
     * 设备编号
     */
    @ExcelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备密钥
     */
    @ExcelProperty(value = "设备密钥")
    private String deviceSecret;

    /**
     * 楼栋ID
     */
    @ExcelProperty(value = "楼栋ID")
    private Long nodeId;

    /**
     * 楼栋路径
     */
    @ExcelProperty(value = "楼栋路径")
    private String nodePath;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private Integer deviceType;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 子设备网关编号
     */
    @ExcelProperty(value = "子设备网关编号")
    private String gwDevCode;

    /**
     * 固件版本
     */
    @ExcelProperty(value = "固件版本")
    private String firmwareVersion;

    /**
     * 信号强度（
     * 信号极好4格[-55— 0]，
     * 信号好3格[-70— -55]，
     * 信号一般2格[-85— -70]，
     * 信号差1格[-100— -85]）
     */
    @ExcelProperty(value = "信号强度", converter = ExcelDictConvert.class)
    private Integer rssi;

    /**
     * 是否启用设备影子(0=禁用，1=启用)
     */
    @ExcelProperty(value = "是否启用设备影子(0=禁用，1=启用)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isShadow;

    /**
     * 定位方式
     */
    @ExcelProperty(value = "定位方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_location_way")
    private Integer locationWay;

    /**
     * 物模型值
     */
    @ExcelProperty(value = "物模型值")
    private JSONArray thingsModelValue;

    /**
     * 设备所在地址
     */
    @ExcelProperty(value = "设备所在地址")
    private String networkAddress;

    /**
     * 设备入网IP
     */
    @ExcelProperty(value = "设备入网IP")
    private String networkIp;

    /**
     * 设备经度
     */
    @ExcelProperty(value = "设备经度")
    private Double longitude;

    /**
     * 设备纬度
     */
    @ExcelProperty(value = "设备纬度")
    private Double latitude;

    /**
     * 激活时间
     */
    @ExcelProperty(value = "激活时间")
    private Date activeTime;

    /**
     * 设备摘要
     */
    @ExcelProperty(value = "设备摘要")
    private String summary;

    /**
     * 图片地址
     */
    @ExcelProperty(value = "图片地址")
    private String imgUrl;

    /**
     * 图片地址Url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "imgUrl")
    private String imgUrlUrl;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 是否是模拟设备
     */
    @ExcelProperty(value = "是否是模拟设备", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_true_false")
    private Boolean isSimulate;

    /**
     * 从机id
     */
    @ExcelProperty(value = "从机id")
    private Integer slaveId;

    /**
     * 设备Mac地址
     */
    private String deviceMac;
    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区ID")
    private Long estateId;

    private Date updateTime;

    private List<StringModelOutput> stringList;
    private List<IntegerModelOutput> integerList;
    private List<DecimalModelOutput> decimalList;
    private List<EnumModelOutput> enumList;
    private List<ArrayModelOutput> arrayList;
    private List<BoolModelOutput> boolList;
    private List<ReadOnlyModelOutput> readOnlyList;

    private List<ThingsModelItem> thingsModels;

    @ExcelProperty(value = "产品标识")
    private String productSymbol;
}
