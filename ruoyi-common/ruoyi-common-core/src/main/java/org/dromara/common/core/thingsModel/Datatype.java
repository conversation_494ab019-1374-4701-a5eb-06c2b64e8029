package org.dromara.common.core.thingsModel;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class Datatype {
    /**
     * 数据类型
     */
    private String type;

    private String falseText;

    private String trueText;

    private BigDecimal min;

    private BigDecimal max;

    private BigDecimal step;

    private String unit;

    private String arrayType;

    private Integer arrayCount;

    private String showWay;

    private int maxLength;

    private List<EnumItem> enumList = new ArrayList<>();

    private List<ThingsModelItem> params;

    private List<ThingsModelItem>[] arrayParams;

    public Datatype() {
        falseText = "";
        trueText = "";
        min = BigDecimal.valueOf(0);
        max = BigDecimal.valueOf(100);
        step = BigDecimal.valueOf(1);
        unit = "";
        arrayType = "";
        arrayCount = 0;
        maxLength = 1024;
    }


}
