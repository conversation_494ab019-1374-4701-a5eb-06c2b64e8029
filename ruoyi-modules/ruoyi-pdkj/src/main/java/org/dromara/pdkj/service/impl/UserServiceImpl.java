package org.dromara.pdkj.service.impl;

import jakarta.annotation.Resource;
import org.dromara.pdkj.domain.User;
import org.dromara.pdkj.domain.bo.UserBo;
import org.dromara.pdkj.mapper.UserMapper;
import org.dromara.pdkj.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class UserServiceImpl implements IUserService {

    @Resource
    private UserMapper userMapper;

    @Override
    public void sync(UserBo userBo) {

        User user = userMapper.selectById(userBo.getUserId());
        if(Objects.isNull(user)){
            user = new User();
            user.setId(userBo.getUserId());
            user.setPhoneNumber(userBo.getMobileNo());
            BeanUtils.copyProperties(userBo,user);
            int insert = userMapper.insert(user);
        }else {
            user.setPhoneNumber(userBo.getMobileNo());
            BeanUtils.copyProperties(userBo,user);
            int update = userMapper.updateById(user);
        }
    }
}
