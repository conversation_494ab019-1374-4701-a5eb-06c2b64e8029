package org.dromara.pdkj.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pdkj.domain.Node;
import org.dromara.pdkj.domain.vo.NodeVo;

import java.util.Collection;
import java.util.List;

/**
 * 楼栋管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface NodeMapper extends BaseMapperPlus<Node, NodeVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default List<NodeVo> selectVoList(Wrapper<Node> wrapper) {
        return BaseMapperPlus.super.selectVoList(wrapper);
    }

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    Long selectCount(@Param(Constants.WRAPPER) Wrapper<Node> queryWrapper);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept")
    })
    default int deleteByIds(Collection<?> idList) {
        return BaseMapperPlus.super.deleteByIds(idList);
    }
}
