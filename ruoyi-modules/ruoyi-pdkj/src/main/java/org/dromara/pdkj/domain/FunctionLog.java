package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 下发日志对象 tb_function_log
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("log.function_log")
public class FunctionLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 标识符
     */
    private String identify;

    /**
     * 操作类型
     */
    private Integer funType;

    /**
     * 日志值
     */
    private String funValue;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 模式
     */
    private Integer mode;

    /**
     * 下发结果描述
     */
    private String resultMsg;

    /**
     * 下发结果代码
     */
    private Integer resultCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示值
     */
    private String showValue;

    /**
     * 物模型名称
     */
    private String modelName;

    /**
     * 设备回复时间
     */
    private Date replyTime;

    /**
     * $column.columnComment
     */
    @TableLogic
    private Long delFlag;


}
