package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.FunctionLog;
import org.dromara.pdkj.domain.bo.FunctionLogBo;
import org.dromara.pdkj.domain.vo.FunctionLogVo;
import org.dromara.pdkj.mapper.FunctionLogMapper;
import org.dromara.pdkj.service.IFunctionLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 下发日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@RequiredArgsConstructor
@Service
public class FunctionLogServiceImpl implements IFunctionLogService {

    private final FunctionLogMapper baseMapper;

    /**
     * 查询下发日志
     *
     * @param id 主键
     * @return 下发日志
     */
    @Override
    public FunctionLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询下发日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 下发日志分页列表
     */
    @Override
    public TableDataInfo<FunctionLogVo> queryPageList(FunctionLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FunctionLog> lqw = buildQueryWrapper(bo);
        Page<FunctionLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的下发日志列表
     *
     * @param bo 查询条件
     * @return 下发日志列表
     */
    @Override
    public List<FunctionLogVo> queryList(FunctionLogBo bo) {
        LambdaQueryWrapper<FunctionLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FunctionLog> buildQueryWrapper(FunctionLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FunctionLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FunctionLog::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getIdentify()), FunctionLog::getIdentify, bo.getIdentify());
        lqw.eq(bo.getFunType() != null, FunctionLog::getFunType, bo.getFunType());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageId()), FunctionLog::getMessageId, bo.getMessageId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), FunctionLog::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), FunctionLog::getDeviceCode, bo.getDeviceCode());
        lqw.eq(bo.getMode() != null, FunctionLog::getMode, bo.getMode());
        lqw.eq(bo.getResultCode() != null, FunctionLog::getResultCode, bo.getResultCode());
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), FunctionLog::getModelName, bo.getModelName());
        lqw.eq(bo.getReplyTime() != null, FunctionLog::getReplyTime, bo.getReplyTime());
        return lqw;
    }

    /**
     * 新增下发日志
     *
     * @param bo 下发日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FunctionLogBo bo) {
        FunctionLog add = MapstructUtils.convert(bo, FunctionLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改下发日志
     *
     * @param bo 下发日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FunctionLogBo bo) {
        FunctionLog update = MapstructUtils.convert(bo, FunctionLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FunctionLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除下发日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
