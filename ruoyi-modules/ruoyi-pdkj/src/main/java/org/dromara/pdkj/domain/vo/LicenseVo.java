package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.pdkj.domain.License;

import java.io.Serial;
import java.io.Serializable;



/**
 * 尚云p2p视图对象 license
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = License.class)
public class LicenseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private Long id;

    /**
     * DID
     */
    @ExcelProperty(value = "DID")
    private String did;

    /**
     * license
     */
    @ExcelProperty(value = "license")
    private String license;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String deviceCode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
