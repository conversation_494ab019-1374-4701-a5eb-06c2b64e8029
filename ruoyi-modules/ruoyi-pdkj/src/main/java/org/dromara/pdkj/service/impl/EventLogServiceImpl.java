package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.EventLog;
import org.dromara.pdkj.domain.bo.EventLogBo;
import org.dromara.pdkj.domain.vo.EventLogVo;
import org.dromara.pdkj.mapper.EventLogMapper;
import org.dromara.pdkj.service.IEventLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 事件日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@RequiredArgsConstructor
@Service
public class EventLogServiceImpl implements IEventLogService {

    private final EventLogMapper baseMapper;

    /**
     * 查询事件日志
     *
     * @param logId 主键
     * @return 事件日志
     */
    @Override
    public EventLogVo queryById(Long logId){
        return baseMapper.selectVoById(logId);
    }

    /**
     * 分页查询事件日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 事件日志分页列表
     */
    @Override
    public TableDataInfo<EventLogVo> queryPageList(EventLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EventLog> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(EventLog::getId);
        Page<EventLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的事件日志列表
     *
     * @param bo 查询条件
     * @return 事件日志列表
     */
    @Override
    public List<EventLogVo> queryList(EventLogBo bo) {
        LambdaQueryWrapper<EventLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EventLog> buildQueryWrapper(EventLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EventLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(EventLog::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getIdentity()), EventLog::getIdentity, bo.getIdentity());
        lqw.eq(bo.getLogType() != null, EventLog::getLogType, bo.getLogType());
        lqw.eq(bo.getDeviceId() != null, EventLog::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), EventLog::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), EventLog::getDeviceCode, bo.getDeviceCode());
        lqw.eq(bo.getIsMonitor() != null, EventLog::getIsMonitor, bo.getIsMonitor());
        lqw.eq(bo.getMode() != null, EventLog::getMode, bo.getMode());
        return lqw;
    }

    /**
     * 新增事件日志
     *
     * @param bo 事件日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EventLogBo bo) {
        EventLog add = MapstructUtils.convert(bo, EventLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改事件日志
     *
     * @param bo 事件日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EventLogBo bo) {
        EventLog update = MapstructUtils.convert(bo, EventLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EventLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除事件日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
