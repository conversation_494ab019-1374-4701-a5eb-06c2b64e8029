package org.dromara.pdkj.domain.bo;

import cn.hutool.json.JSONObject;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.ThingsModel;

/**
 * 物模型业务对象 tb_things_model
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ThingsModel.class, reverseConvertGenerate = false)
public class ThingsModelBo extends BaseEntity {

    /**
     * 物模型ID
     */
    @NotNull(message = "物模型ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 物模型名称
     */
    @NotBlank(message = "物模型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String modelName;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 标识符
     */
    @NotBlank(message = "标识符不能为空", groups = { AddGroup.class, EditGroup.class })
    private String identifier;

    /**
     * 模型类别
     */
    @NotNull(message = "模型类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer type;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String datatype;

    /**
     * 数据定义
     */
    private JSONObject specs;

    /**
     * 是否图表展示
     */
    @NotNull(message = "是否图表展示不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean isChart;

    /**
     * 是否实时监测
     */
    @NotNull(message = "是否实时监测不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean isMonitor;

    /**
     * 是否历史存储
     */
    private Boolean isHistory;

    /**
     * 是否只读数据
     */
    private Boolean isReadonly;

    /**
     * 设备分享权限
     */
    private Boolean isSharePerm;

    /**
     * 排序，值越大，排序越靠前
     */
    private Integer modelOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 从机id
     */
    private String tempSlaveId;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 控制公式
     */
    private String reverseFormula;

    /**
     * 寄存器地址值
     */
    private Long regAddr;

    /**
     * 位定义选项
     */
    private String bitOption;

    /**
     * 解析类型
     */
    private Integer valueType;

    /**
     * 是计算参数
     */
    private Boolean isParams;

    /**
     * 读取寄存器数量
     */
    private Integer quantity;

    /**
     * modbus功能码
     */
    private String code;

    /**
     * modbus解析类型
     */
    private String parseType;


}
