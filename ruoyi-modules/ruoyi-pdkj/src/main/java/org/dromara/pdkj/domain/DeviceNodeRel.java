package org.dromara.pdkj.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备楼栋关联对象 device_node_rel
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@TableName("device.device_node_rel")
public class DeviceNodeRel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 楼栋ID
     */
    private Long nodeId;

    /**
     * 小区ID
     */
    private Long estateId;

    /**
     * 楼栋路径
     */
    private String nodePath;

    /**
     * 客户端用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
