package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.DeviceNodeRel;
import org.dromara.pdkj.domain.bo.DeviceNodeRelBo;
import org.dromara.pdkj.domain.vo.DeviceNodeRelVo;
import org.dromara.pdkj.mapper.DeviceNodeRelMapper;
import org.dromara.pdkj.service.IDeviceNodeRelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备楼栋关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RequiredArgsConstructor
@Service
public class DeviceNodeRelServiceImpl implements IDeviceNodeRelService {

    private final DeviceNodeRelMapper baseMapper;

    /**
     * 查询设备楼栋关联
     *
     * @param id 主键
     * @return 设备楼栋关联
     */
    @Override
    public DeviceNodeRelVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备楼栋关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备楼栋关联分页列表
     */
    @Override
    public TableDataInfo<DeviceNodeRelVo> queryPageList(DeviceNodeRelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceNodeRel> lqw = buildQueryWrapper(bo);
        Page<DeviceNodeRelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备楼栋关联列表
     *
     * @param bo 查询条件
     * @return 设备楼栋关联列表
     */
    @Override
    public List<DeviceNodeRelVo> queryList(DeviceNodeRelBo bo) {
        LambdaQueryWrapper<DeviceNodeRel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceNodeRel> buildQueryWrapper(DeviceNodeRelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceNodeRel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceNodeRel::getId);
        lqw.eq(bo.getDeviceId() != null, DeviceNodeRel::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getNodeId() != null, DeviceNodeRel::getNodeId, bo.getNodeId());
        lqw.eq(bo.getEstateId() != null, DeviceNodeRel::getEstateId, bo.getEstateId());
        lqw.like(StringUtils.isNotBlank(bo.getNodePath()), DeviceNodeRel::getNodePath, bo.getNodePath());
        return lqw;
    }

    /**
     * 新增设备楼栋关联
     *
     * @param bo 设备楼栋关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceNodeRelBo bo) {
        DeviceNodeRel add = MapstructUtils.convert(bo, DeviceNodeRel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备楼栋关联
     *
     * @param bo 设备楼栋关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceNodeRelBo bo) {
        DeviceNodeRel update = MapstructUtils.convert(bo, DeviceNodeRel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceNodeRel entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备楼栋关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
