package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.Protocol;
import org.dromara.pdkj.domain.bo.ProtocolBo;
import org.dromara.pdkj.domain.vo.ProtocolVo;
import org.dromara.pdkj.mapper.ProtocolMapper;
import org.dromara.pdkj.service.IProtocolService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 协议管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@RequiredArgsConstructor
@Service
public class ProtocolServiceImpl implements IProtocolService {

    private final ProtocolMapper baseMapper;

    /**
     * 查询协议管理
     *
     * @param id 主键
     * @return 协议管理
     */
    @Override
    public ProtocolVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询协议管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 协议管理分页列表
     */
    @Override
    public TableDataInfo<ProtocolVo> queryPageList(ProtocolBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Protocol> lqw = buildQueryWrapper(bo);
        Page<ProtocolVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的协议管理列表
     *
     * @param bo 查询条件
     * @return 协议管理列表
     */
    @Override
    public List<ProtocolVo> queryList(ProtocolBo bo) {
        LambdaQueryWrapper<Protocol> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Protocol> buildQueryWrapper(ProtocolBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Protocol> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Protocol::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getProtocolCode()), Protocol::getProtocolCode, bo.getProtocolCode());
        lqw.like(StringUtils.isNotBlank(bo.getProtocolName()), Protocol::getProtocolName, bo.getProtocolName());
        lqw.eq(bo.getProtocolStatus() != null, Protocol::getProtocolStatus, bo.getProtocolStatus());
        return lqw;
    }

    /**
     * 新增协议管理
     *
     * @param bo 协议管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProtocolBo bo) {
        Protocol add = MapstructUtils.convert(bo, Protocol.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改协议管理
     *
     * @param bo 协议管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProtocolBo bo) {
        Protocol update = MapstructUtils.convert(bo, Protocol.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Protocol entity) {
        entity.setProtocolCode(entity.getProtocolCode().toUpperCase());
    }

    /**
     * 校验并批量删除协议管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
