<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-vue-plus</artifactId>
        <version>5.3.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-common-oss</artifactId>

    <description>
        ruoyi-common-oss oss服务
    </description>

    <dependencies>
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>ruoyi-common-json</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>ruoyi-common-redis</artifactId>
        </dependency>

        <!--  AWS SDK for Java 2.x  -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <exclusions>
                <!-- 将基于 Netty 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
                <!-- 将基于 CRT 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>aws-crt-client</artifactId>
                </exclusion>
                <!-- 将基于 Apache 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <!-- 将配置基于 URL 连接的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>url-connection-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 使用AWS基于 CRT 的 S3 客户端 -->
        <dependency>
            <groupId>software.amazon.awssdk.crt</groupId>
            <artifactId>aws-crt</artifactId>
        </dependency>

        <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
        </dependency>
    </dependencies>

</project>
