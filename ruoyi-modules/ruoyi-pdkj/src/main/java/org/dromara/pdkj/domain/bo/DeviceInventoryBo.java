package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.DeviceInventory;

import java.util.Date;

/**
 * 设备出厂业务对象 device_inventory
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DeviceInventory.class, reverseConvertGenerate = false)
public class DeviceInventoryBo extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 产品密钥
     */
    @NotBlank(message = "产品密钥不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceSecret;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceCode;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备别名
     */
    private String deviceAlias;

    /**
     * 设备状态
     */
    @NotNull(message = "设备状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer deviceStatus;

    /**
     * 激活时间
     */
    private Date lastActivateTime;

    /**
     * 注销时间
     */
    private Date lastDeactivateTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 打开时间
     */
    @NotNull(message = "打开时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bindStatus;


    private String deviceMac;
}
