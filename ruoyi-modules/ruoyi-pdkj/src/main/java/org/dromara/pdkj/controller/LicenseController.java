package org.dromara.pdkj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pdkj.domain.bo.LicenseBo;
import org.dromara.pdkj.domain.vo.LicenseVo;
import org.dromara.pdkj.service.ILicenseService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 尚云p2p
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdkj/license")
public class LicenseController extends BaseController {

    private final ILicenseService licenseService;

    /**
     * 查询尚云p2p列表
     */
    @SaCheckPermission("pdkj:license:list")
    @GetMapping("/list")
    public TableDataInfo<LicenseVo> list(LicenseBo bo, PageQuery pageQuery) {
        return licenseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出尚云p2p列表
     */
    @SaCheckPermission("pdkj:license:export")
    @Log(title = "尚云p2p", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LicenseBo bo, HttpServletResponse response) {
        List<LicenseVo> list = licenseService.queryList(bo);
        ExcelUtil.exportExcel(list, "尚云p2p", LicenseVo.class, response);
    }

    /**
     * 获取尚云p2p详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pdkj:license:query")
    @GetMapping("/{id}")
    public R<LicenseVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        return R.ok(licenseService.queryById(id));
    }

    /**
     * 新增尚云p2p
     */
    @SaCheckPermission("pdkj:license:add")
    @Log(title = "尚云p2p", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LicenseBo bo) {
        return toAjax(licenseService.insertByBo(bo));
    }

    /**
     * 修改尚云p2p
     */
    @SaCheckPermission("pdkj:license:edit")
    @Log(title = "尚云p2p", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LicenseBo bo) {
        return toAjax(licenseService.updateByBo(bo));
    }

    /**
     * 删除尚云p2p
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pdkj:license:remove")
    @Log(title = "尚云p2p", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(licenseService.deleteWithValidByIds(List.of(ids), true));
    }
}
