package org.dromara.pdkj.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.bo.CategoryBo;
import org.dromara.pdkj.domain.vo.CategoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品类型Service接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface ICategoryService {

    /**
     * 查询产品类型
     *
     * @param id 主键
     * @return 产品类型
     */
    CategoryVo queryById(Long id);

    /**
     * 分页查询产品类型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品类型分页列表
     */
    TableDataInfo<CategoryVo> queryPageList(CategoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品类型列表
     *
     * @param bo 查询条件
     * @return 产品类型列表
     */
    List<CategoryVo> queryList(CategoryBo bo);

    /**
     * 新增产品类型
     *
     * @param bo 产品类型
     * @return 是否新增成功
     */
    Boolean insertByBo(CategoryBo bo);

    /**
     * 修改产品类型
     *
     * @param bo 产品类型
     * @return 是否修改成功
     */
    Boolean updateByBo(CategoryBo bo);

    /**
     * 校验并批量删除产品类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
