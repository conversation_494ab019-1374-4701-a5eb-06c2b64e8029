package org.dromara.mqtt.handler.mqtt.impl;


import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.mqtt.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.base.util.AttributeUtils;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.mqtt.annotation.Process;
import org.dromara.mqtt.handler.mqtt.MqttHandler;
import org.dromara.mqtt.manager.ClientManager;
import org.dromara.mqtt.manager.ResponseManager;
import org.dromara.mqtt.manager.RetainMsgManager;
import org.dromara.mqtt.model.ClientMessage;
import org.dromara.mqtt.model.RetainMessage;
import org.dromara.mqtt.utils.MqttMessageUtils;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Process(type = MqttMessageType.SUBSCRIBE)
public class MqttSubscribe implements MqttHandler {


    @Override
    public void handler(ChannelHandlerContext ctx, MqttMessage message) {
        subscribe(ctx, (MqttSubscribeMessage) message);
    }


    public void subscribe(ChannelHandlerContext ctx, MqttSubscribeMessage message) {
        /*获取session*/
        Session session = AttributeUtils.getSession(ctx.channel());
        /*获取客户端订阅的topic列表*/
        List<MqttTopicSubscription> topList = message.payload().topicSubscriptions();
        /*获取topicName列表*/
        List<String> topicNameList = topList.stream().map(MqttTopicSubscription::topicName).collect(Collectors.toList());
        log.debug("=>客户端:{},订阅主题:{}", session.getClientId(), JsonUtils.toJsonString(topicNameList));
        if (!TopicsUtils.validTopicFilter(topicNameList)) {
            log.error("=>订阅主题不合法:{}", JsonUtils.toJsonString(topicNameList));
            return;
        }
        /*存储到本地topic缓存*/
        topicNameList.forEach(topicName -> {
            ClientManager.push(topicName, session);
            /*累计订阅数*/
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_SUBSCRIBE_TOTAL, -1L);
        });
        /*更新客户端ping*/
        ClientManager.updatePing(session.getClientId());
        /*应答客户端订阅成功*/
        MqttSubAckMessage subAckMessage = MqttMessageUtils.buildSubAckMessage(message);
        ResponseManager.responseMessage(session, subAckMessage, true);
        /*客户端订阅了遗留消息主题后，推送遗留消息给客户端*/
        topList.forEach(topic -> {
            retain(topic.topicName(), session, topic.qualityOfService());
        });
    }


    /**
     * 推送遗留消息
     *
     * @param topicName 主题
     * @param session   客户端
     * @param mqttQoS   消息质量
     */
    @SneakyThrows
    private void retain(String topicName, Session session, MqttQoS mqttQoS) {
        RetainMessage message = RetainMsgManager.getRetain(topicName);
        if (null == message) {
            return;
        }
        MqttQoS qos = message.getQos() > mqttQoS.value() ? mqttQoS : MqttQoS.valueOf(message.getQos());
        switch (qos.value()) {
            case 0:
                buildMessage(qos, topicName, 0, message.getMessage(), session);
                break;
            case 1:
            case 2:
                /*使用实时时间戳充当 packId*/
                buildMessage(qos, topicName, (int) System.currentTimeMillis(), message.getMessage(), session);
                break;
        }
    }

    /*组装推送数据*/
    private void buildMessage(MqttQoS qos, String topicName, int packetId, byte[] message, Session session) {
        /*生成客户端model*/
        ClientMessage clientMessage = ClientMessage.of(qos, topicName, false, message);
        /*组建推送消息*/
        MqttPublishMessage publishMessage = MqttMessageUtils.buildPublishMessage(clientMessage, packetId);
        /*推送消息*/
        ResponseManager.publishClients(publishMessage, session);
    }


}
