package org.dromara.pdkj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pdkj.domain.DeviceLog;
import org.dromara.pdkj.domain.bo.DeviceLogBo;
import org.dromara.pdkj.domain.vo.DeviceLogVo;
import org.dromara.pdkj.mapper.DeviceLogMapper;
import org.dromara.pdkj.service.IDeviceLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RequiredArgsConstructor
@Service
public class DeviceLogServiceImpl implements IDeviceLogService {

    private final DeviceLogMapper baseMapper;

    /**
     * 查询设备日志
     *
     * @param id 主键
     * @return 设备日志
     */
    @Override
    public DeviceLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备日志分页列表
     */
    @Override
    public TableDataInfo<DeviceLogVo> queryPageList(DeviceLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceLog> lqw = buildQueryWrapper(bo);
        Page<DeviceLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备日志列表
     *
     * @param bo 查询条件
     * @return 设备日志列表
     */
    @Override
    public List<DeviceLogVo> queryList(DeviceLogBo bo) {
        LambdaQueryWrapper<DeviceLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceLog> buildQueryWrapper(DeviceLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceLog::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getIdentity()), DeviceLog::getIdentity, bo.getIdentity());
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), DeviceLog::getModelName, bo.getModelName());
        lqw.eq(bo.getLogType() != null, DeviceLog::getLogType, bo.getLogType());
        lqw.eq(StringUtils.isNotBlank(bo.getLogValue()), DeviceLog::getLogValue, bo.getLogValue());
        lqw.eq(bo.getDeviceId() != null, DeviceLog::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), DeviceLog::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceCode()), DeviceLog::getDeviceCode, bo.getDeviceCode());
        lqw.eq(bo.getIsMonitor() != null, DeviceLog::getIsMonitor, bo.getIsMonitor());
        lqw.eq(bo.getMode() != null, DeviceLog::getMode, bo.getMode());
        return lqw;
    }

    /**
     * 新增设备日志
     *
     * @param bo 设备日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceLogBo bo) {
        DeviceLog add = MapstructUtils.convert(bo, DeviceLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备日志
     *
     * @param bo 设备日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceLogBo bo) {
        DeviceLog update = MapstructUtils.convert(bo, DeviceLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
