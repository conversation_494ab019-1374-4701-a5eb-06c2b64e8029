package org.dromara.pdkj.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.pdkj.domain.Estate;

import java.io.Serial;
import java.io.Serializable;



/**
 * 小区信息视图对象 tb_estate
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Estate.class)
public class EstateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String estateName;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 楼栋结构
     */
    @ExcelProperty(value = "楼栋结构")
    private String nodeStruct;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;


}
