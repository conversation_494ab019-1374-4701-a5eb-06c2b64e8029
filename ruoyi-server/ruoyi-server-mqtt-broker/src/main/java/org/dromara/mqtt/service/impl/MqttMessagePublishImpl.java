package org.dromara.mqtt.service.impl;


import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import io.netty.buffer.Unpooled;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.base.session.Session;
import org.dromara.collect.service.ProtocolService;
import org.dromara.common.core.constant.PdkjConstant;
import org.dromara.common.core.enums.FunctionReplyStatus;
import org.dromara.common.core.enums.ServerType;
import org.dromara.common.core.enums.TopicType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.gateway.CRC16Utils;
import org.dromara.common.core.gateway.mq.TopicsUtils;
import org.dromara.common.core.mq.NtpModel;
import org.dromara.common.core.mq.bo.DeviceReportBo;
import org.dromara.common.core.mq.bo.MQSendMessageBo;
import org.dromara.common.core.mq.message.DeviceData;
import org.dromara.common.core.mq.message.DeviceDownMessage;
import org.dromara.common.core.mq.message.InstructionsMessage;
import org.dromara.common.core.mq.message.MqttBo;
import org.dromara.common.core.mq.ota.OtaUpgradeBo;
import org.dromara.common.core.protocol.modbus.ModbusCode;
import org.dromara.common.core.thingsModel.ThingsModelSimpleItem;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.SnowflakeIdWorker;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ip.IpUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.mq.model.ReportDataBo;
import org.dromara.mq.service.IMqttMessagePublish;
import org.dromara.mqtt.manager.MqttRemoteManager;
import org.dromara.mqtt.manager.SessionManger;
import org.dromara.mqtt.model.PushMessageBo;
import org.dromara.mqttclient.PubMqttClient;
import org.dromara.pdkj.domain.Device;
import org.dromara.pdkj.domain.FunctionLog;
import org.dromara.pdkj.domain.dto.PropertyDto;
import org.dromara.pdkj.domain.vo.ProductVo;
import org.dromara.pdkj.mapper.DeviceMapper;
import org.dromara.pdkj.mapper.FunctionLogMapper;
import org.dromara.pdkj.service.IProductService;
import org.dromara.pdkj.service.IThingsModelService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 消息推送方法集合
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttMessagePublishImpl implements IMqttMessagePublish {

    @Resource
    private IProductService productService;
    @Resource
    private PubMqttClient mqttClient;
    @Resource
    private TopicsUtils topicsUtils;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private FunctionLogMapper functionLogMapper;
    @Resource
    private MqttRemoteManager remoteManager;

    @Resource
    private IThingsModelService thingsModelService;
    @Resource
    private ProtocolService protocolService;
    private SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(3);

//    @Resource
//    private RuleProcess ruleProcess;


    @Override
    public InstructionsMessage buildMessage(DeviceDownMessage downMessage, TopicType type) {
        /*返回的组将数据*/
        InstructionsMessage message = new InstructionsMessage();
        /*根据设备编号查询产品信息*/
        if (StringUtils.isEmpty(downMessage.getProtocolCode())) {
            ProductVo product = productService.getProductBySerialNumber(downMessage.getDeviceCode());
            Optional.ofNullable(product).orElseThrow(() -> new ServiceException("产品为空"));
            downMessage.setProtocolCode(product.getProtocolCode());
        }
        String deviceCode = downMessage.getDeviceCode() == null ? "" : downMessage.getDeviceCode();

        /*组建Topic*/
        String topicName = "";
        if (downMessage.getServerType().equals(ServerType.MQTT)) {
            topicName = topicsUtils.buildTopic(downMessage.getProductId(), deviceCode, type);
        } else if (downMessage.getServerType().equals(ServerType.TCP)) {
            topicName = downMessage.getDeviceCode();
        }

        DeviceData encodeData = DeviceData.builder()
            .downMessage(downMessage)
            .deviceCode(deviceCode)
            .extra(downMessage.getExtra())
            .body(downMessage.getBody())
            .code(downMessage.getCode())
            .userId(downMessage.getUserId())
            .userType(downMessage.getUserType())
            .topicName(topicName).build();
        //根据协议编码后数据
        byte[] data = protocolService.getProtocol(downMessage.getProtocolCode()).encode(encodeData, null);
        message.setMessage(data);
        message.setDeviceCode(deviceCode);
        message.setTopicName(topicName);

        return message;
    }

    /**
     * 服务(指令)下发
     *
     * @return
     */
    @Override
    public ReportDataBo funcSend(MQSendMessageBo bo) {
        //如果协议编号为空，则获取
        if (StringUtils.isEmpty(bo.getProtocolCode())) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product == null) {
                throw new ServiceException("产品[" + bo.getProductId() + "]不存在");
            }
            //bo.setType(ThingsModelType.SERVICE);
            bo.setProtocolCode(product.getProtocolCode());
            bo.setTransport(product.getTransport());
        }

        Device device = deviceMapper.selectDeviceByDeviceCode(bo.getDeviceCode());
        if (device == null) {
            throw new ServiceException("设备[" + bo.getDeviceCode() + "]不存在");
        }
        if (!Objects.equals(device.getProductId(), bo.getProductId())) {
            throw new ServiceException("设备[" + bo.getDeviceCode() + "]产品[" + bo.getProductId() + "]不匹配");
        }

        //处理设备影子模式 todo
//        if (null != bo.getIsShadow() && bo.getIsShadow()) {
        List<ThingsModelSimpleItem> dataList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : bo.getValue()) {
            ThingsModelSimpleItem item = new ThingsModelSimpleItem(entry.getKey(), entry.getValue().toString(), "");
            dataList.add(item);
        }
        ReportDataBo dataBo = new ReportDataBo();
        dataBo.setProductId(bo.getProductId());
        dataBo.setDeviceCode(bo.getDeviceCode());
        dataBo.setRuleEngine(false);
        dataBo.setShadow(device.getIsShadow());
        dataBo.setSlaveId(bo.getSlaveId());
        dataBo.setType(bo.getType().getCode());
        dataBo.setDataList(dataList);
//        }

        /* 下发服务数据存储对象*/
        FunctionLog funcLog = new FunctionLog();
        funcLog.setCreateTime(DateUtils.getNowDate());
        funcLog.setFunValue(bo.getValue() == null ? null : bo.getValue().getStr(bo.getIdentifier()));
        funcLog.setMessageId(bo.getMessageId());
        funcLog.setDeviceCode(bo.getDeviceCode());
        funcLog.setIdentify(bo.getIdentifier());
        funcLog.setShowValue(bo.getShowValue());
        funcLog.setFunType(1);
        funcLog.setModelName(bo.getModelName());
        //兼容子设备
        if (null != bo.getSlaveId()) {
            PropertyDto thingModels = thingsModelService.getSingleThingModels(bo.getProductId(), bo.getIdentifier() + "#" + bo.getSlaveId());
            funcLog.setDeviceCode(bo.getDeviceCode() + "_" + bo.getSlaveId());
            bo.setCode(ModbusCode.Write06);
            if (thingModels != null && !Objects.isNull(thingModels.getCode())) {
                bo.setCode(ModbusCode.getInstance(Integer.parseInt(thingModels.getCode())));
            }
        }

        ServerType serverType = ServerType.explain(bo.getTransport());
        Optional.ofNullable(serverType).orElseThrow(() -> new ServiceException("产品的传输协议编码为空!"));
        /*下发服务数据处理对象*/
        DeviceDownMessage downMessage = DeviceDownMessage.builder()
            .messageId(bo.getMessageId())
            .body(bo.getValue())
            .deviceCode(bo.getDeviceCode())
            .productId(bo.getProductId())
            .timestamp(DateUtils.getTimestamp())
            .identifier(bo.getIdentifier())
            .slaveId(bo.getSlaveId())
            .code(bo.getCode() == ModbusCode.Read01 ? ModbusCode.Write05 : ModbusCode.Write06)
            .serverType(serverType)
            .extra(bo.getExtra())
            .userId(bo.getUserId())
            .userType(bo.getUserType())
            .build();

        //组建下发服务指令
        InstructionsMessage instruction = buildMessage(downMessage, TopicType.FUNCTION_GET);
        switch (serverType) {
            case MQTT:
                //  规则引擎脚本处理,完成后返回结果
//                MsgContext context = ruleProcess.processRuleScript(bo.getSerialNumber(), 2, instruction.getTopicName(), new String(instruction.getMessage()));
//                if (!Objects.isNull(context) && StringUtils.isNotEmpty(context.getPayload())
//                    && StringUtils.isNotEmpty(context.getTopic())) {
//                    instruction.setTopicName(context.getTopic());
//                    instruction.setMessage(context.getPayload().getBytes());
//                }
                publish(instruction.getTopicName(), instruction.getMessage(), funcLog);
                log.debug("=>MQTT服务下发,topic=[{}],指令=[{}]", instruction.getTopicName(), new String(instruction.getMessage()));
                break;
            case TCP:
                sendByTcp(instruction.getTopicName(), instruction.getMessage(), funcLog);
                log.debug("=>TCP服务下发,设备编号=[{}],指令=[{}],原始内容=[{}]", instruction.getTopicName(), HexUtil.encodeHexStr(instruction.getMessage()), new String(instruction.getMessage()));
                break;

        }


        return dataBo;
    }

    public void publish(String topic, byte[] pushMessage, FunctionLog functionLog) {
        try {
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_SEND_TOTAL, -1L);
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_SEND_TODAY, 60 * 60 * 24);
            mqttClient.publish(pushMessage, topic, false, 0);
            if (null != functionLog) {
                //存储服务下发成功
                functionLog.setResultMsg(FunctionReplyStatus.NORELY.getMessage());
                functionLog.setResultCode(FunctionReplyStatus.NORELY.getCode());
                functionLogMapper.insert(functionLog);
            }
        } catch (Exception e) {
            if (null != functionLog) {
                //服务下发失败存储
                functionLog.setResultMsg(FunctionReplyStatus.FAIl.getMessage() + "原因: " + e.getMessage());
                functionLog.setResultCode(FunctionReplyStatus.FAIl.getCode());
                functionLogMapper.insert(functionLog);
            }
        }
    }

    public void sendByTcp(String deviceCode, byte[] pushMessage, FunctionLog functionLog) {
        try {
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_SEND_TOTAL, -1L);
            RedisUtils.incr2(PdkjConstant.REDIS.MESSAGE_SEND_TODAY, 60 * 60 * 24);

            Session session = SessionManger.getSession(deviceCode);
            if (session == null || session.getHandlerContext() == null) {
                throw new ServiceException("设备不在线");
            }

            log.info("tcp发送消息的channelId: {}, {}", deviceCode, session.getHandlerContext().channel().id().asLongText());
            session.getHandlerContext().channel().writeAndFlush(Unpooled.copiedBuffer(pushMessage));
            if (null != functionLog) {
                //存储服务下发成功
                functionLog.setResultMsg(FunctionReplyStatus.NORELY.getMessage());
                functionLog.setResultCode(FunctionReplyStatus.NORELY.getCode());
                functionLogMapper.insert(functionLog);
            }
        } catch (Exception e) {
            if (null != functionLog) {
                //服务下发失败存储
                functionLog.setResultMsg(FunctionReplyStatus.FAIl.getMessage() + "原因: 【" + deviceCode + "]" + e.getMessage());
                functionLog.setResultCode(FunctionReplyStatus.FAIl.getCode());
                functionLogMapper.insert(functionLog);
            }
        }
    }

    /**
     * OTA升级下发
     *
     * @param bo
     */
    @Override
    public void upGradeOTA(OtaUpgradeBo bo) {

    }

    @Override
    public void sendFunctionMessage(DeviceReportBo bo) {
        log.warn("=>功能指令下发,sendFunctionMessage bo=[{}]", bo);
        Device device = deviceMapper.selectDeviceByDeviceCode(bo.getDeviceCode());
        Optional.ofNullable(device).orElseThrow(() -> new ServiceException("服务下发的设备:[" + bo.getDeviceCode() + "]不存在"));

        ProductVo productVo = productService.queryById(topicsUtils.parseProductId(bo.getTopicName()));
        ServerType serverType = ServerType.explain(productVo.getTransport());
        Optional.ofNullable(serverType).orElseThrow(() -> new ServiceException("产品的传输协议编码为空!"));

        switch (serverType) {
            case GB28181:
                break;
        }
    }

    /**
     * 1.发布设备状态
     */
    @Override
    public void publishStatus(Long productId, String deviceNum, int deviceStatus, Boolean isShadow, int rssi) {
        String message = "{\"status\":" + deviceStatus + ",\"isShadow\":" + isShadow + ",\"rssi\":" + rssi + "}";
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.STATUS_POST);
        mqttClient.publish(1, false, topic, message);
    }


    /**
     * 2.发布设备信息
     */
    @Override
    public void publishInfo(Long productId, String deviceNum) {
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.INFO_GET);
        mqttClient.publish(1, false, topic, "");
    }

    /**
     * 3.发布时钟同步信息
     *
     * @param bo 数据模型
     */
    public void publishNtp(ReportDataBo bo) {
        NtpModel ntpModel = JSONUtil.toBean(bo.getMessage(), NtpModel.class);
        ntpModel.setServerRecvTime(System.currentTimeMillis());
        ntpModel.setServerSendTime(System.currentTimeMillis());
        String topic = topicsUtils.buildTopic(bo.getProductId(), bo.getDeviceCode(), TopicType.NTP_GET);
        mqttClient.publish(1, false, topic, JSONUtil.toJsonStr(ntpModel));
    }

    /**
     * 4.发布属性
     * delay 延时，秒为单位
     */
    @Override
    public void publishProperty(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay) {
        String pre = "";
        if (delay > 0) {
            pre = "$delayed/" + String.valueOf(delay) + "/";
        }
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.FUNCTION_GET);
        if (thingsList == null) {
            mqttClient.publish(1, true, topic, "");
        } else {
            mqttClient.publish(1, true, topic, JSONUtil.toJsonStr(thingsList));
        }
    }

    /**
     * 5.发布功能
     * delay 延时，秒为单位
     */
    @Override
    public void publishFunction(Long productId, String deviceNum, List<ThingsModelSimpleItem> thingsList, int delay) {
        String pre = "";
        if (delay > 0) {
            pre = "$delayed/" + String.valueOf(delay) + "/";
        }
        String topic = topicsUtils.buildTopic(productId, deviceNum, TopicType.FUNCTION_GET);
        if (thingsList == null) {
            mqttClient.publish(1, true, topic, "");
        } else {
            mqttClient.publish(1, true, topic, JSONUtil.toJsonStr(thingsList));
        }

    }

    /**
     * 设备数据同步
     *
     * @param deviceNumber 设备编号
     * @return 设备
     */
    public Device deviceSynchronization(String deviceNumber) {
        Device device = deviceMapper.selectDeviceByDeviceCode(deviceNumber);
        // 1-未激活，2-禁用，3-在线，4-离线
        if (device.getDeviceStatus() == 3) {
            device.setDeviceStatus(4);
            deviceMapper.updateById(device);
            // 发布设备信息
            publishInfo(device.getProductId(), device.getDeviceCode());
        }
        return device;
    }


    /**
     * 发送模拟设备到WS
     */
    public void sendSimulationWs(MqttBo send, MqttBo receive, String topic) {
        PushMessageBo messageBo = new PushMessageBo();
        messageBo.setTopic(topic);
        JSONArray array = new JSONArray();
        send.setDirection("send");
        send.setTs(DateUtils.getNowDate());
        receive.setTs(DateUtils.getNowDate());
        receive.setDirection("receive");
        array.add(send);
        array.add(receive);
        messageBo.setMessage(array.toString());
        remoteManager.pushCommon(messageBo);
    }

    public byte[] CRC(byte[] source) {
        source[2] = (byte) ((int) source[2] * 2);
        byte[] result = new byte[source.length + 2];
        byte[] crc16Byte = CRC16Utils.getCrc16Byte(source);
        System.arraycopy(source, 0, result, 0, source.length);
        System.arraycopy(crc16Byte, 0, result, result.length - 2, 2);
        return result;
    }


    /**
     * 搭建消息
     *
     * @param bo
     * @return
     */
    private DeviceDownMessage buildMessage(OtaUpgradeBo bo) {
        String messageId = String.valueOf(snowflakeIdWorker.nextId());
        bo.setMessageId(messageId);
        bo.setOtaUrl("http://" + IpUtils.getHostIp() + bo.getOtaUrl());
        return DeviceDownMessage.builder()
            .productId(bo.getProductId())
            .deviceCode(bo.getDeviceCode())
            .body(JSONUtil.parseObj(bo))
            .timestamp(DateUtils.getTimestamp())
            .messageId(messageId)
            .build();

    }

}
