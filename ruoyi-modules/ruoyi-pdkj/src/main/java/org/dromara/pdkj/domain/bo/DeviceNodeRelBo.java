package org.dromara.pdkj.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.pdkj.domain.DeviceNodeRel;

/**
 * 设备楼栋关联业务对象 device_node_rel
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DeviceNodeRel.class, reverseConvertGenerate = false)
public class DeviceNodeRelBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 楼栋ID
     */
    @NotNull(message = "楼栋ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long nodeId;

    /**
     * 设备code
     */
    @NotNull(message = "ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 小区ID
     */
    @NotNull(message = "小区ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long estateId;

    /**
     * 楼栋路径
     */
    @NotBlank(message = "楼栋路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nodePath;


}
