package org.dromara.pdkj.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.HutoolJsonObjectTypeHandler;

import java.io.Serial;

/**
 * 通用物模型对象 tb_things_model_template
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "iot.things_model_template", autoResultMap = true)
public class ThingsModelTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物模型ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 物模型名称
     */
    private String templateName;

    /**
     * 标识符
     */
    private String identifier;

    /**
     * 模型类别
     */
    private Integer type;

    /**
     * 数据类型
     */
    private String datatype;

    /**
     * 数据定义
     */
    @TableField(typeHandler = HutoolJsonObjectTypeHandler.class)
    private JSONObject specs;

    /**
     * 系统通用
     */
    private Boolean isSys;

    /**
     * 图表展示
     */
    private Boolean isChart;

    /**
     * 实时监测
     */
    private Boolean isMonitor;

    /**
     * 历史存储
     */
    private Boolean isHistory;

    /**
     * 只读数据
     */
    private Boolean isReadonly;

    /**
     * 设备分享
     */
    private Boolean isSharePerm;

    /**
     * 排序
     */
    private Integer modelOrder;

    /**
     * 删除标志
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 从机id
     */
    private String tempSlaveId;

    /**
     * 计算公式
     */
    private String formula;

    /**
     * 控制公式
     */
    private String reverseFormula;

    /**
     * 寄存器地址值
     */
    private Integer regAddr;

    /**
     * 位定义选项
     */
    private String bitOption;

    /**
     * 解析类型 1.数值 2.选项
     */
    private Integer valueType;

    /**
     * 计算参数
     */
    private Boolean isParams;

    /**
     * 读取寄存器数量
     */
    private Integer quantity;

    /**
     * modbus功能码
     */
    private String code;

    /**
     * 旧的标识符
     */
    private String oldIdentifier;

    /**
     * 旧的从机id
     */
    private String oldTempSlaveId;

    /**
     * modbus解析类型
     */
    private String parseType;


}
